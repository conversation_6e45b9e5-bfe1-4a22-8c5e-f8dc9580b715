﻿@using Microsoft.AspNetCore.Components
@using MudBlazor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Grids
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@page "/PlanLabsGrid"
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILabTestsService _labTestsService
@inject PatientService _PatientService
@inject IAssessmentsService assessmentsService
<div style="display: flex; flex-direction: column;">
    <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
        <MudGrid Spacing="3" Style="align-items: center;">
            <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                <MudButton Color="Color.Primary"
                OnClick="AddNewHistory"
                Variant="Variant.Filled"
                Dense="true"
                Style="min-width: 70px; height: 35px;">
                    @Localizer["Add"]
                </MudButton>
            </MudItem>
        </MudGrid>
        <SfGrid @ref="PlanLabsGridRef" TValue="LabTests" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@planlabsRelatedToAssessments" AllowPaging="true" PageSettings-PageSize="5">
            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
            <GridPageSettings PageSize="10"></GridPageSettings>
            <GridEvents OnActionComplete="ActionCompletedHandler" TValue="LabTests"></GridEvents>
            <GridColumns>
                <GridColumn Field="LabTestsId" IsPrimaryKey="true" Visible="false"></GridColumn>
                <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field="LabTest1" HeaderText="@Localizer["Primary Test"]" Width="40" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                <GridColumn Field="TestOrganization" HeaderText="@Localizer["Organization"]" Width="40" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"
                EditTemplate="@OrganizationEditTemplate"></GridColumn>
                <GridColumn Field="@nameof(LabTests.AssessmentData)"
                HeaderText="@Localizer["RelatedAssessment"]"
                TextAlign="TextAlign.Center"
                Width="100"
                EditTemplate="@AssessmentEditTemplate">
                </GridColumn>
                <GridColumn HeaderText="@Localizer["Actions"]" Width="15" TextAlign="TextAlign.Center">
                    <GridCommandColumns>
                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                    </GridCommandColumns>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
    <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
        <MudButton Color="Color.Secondary"
        Variant="Variant.Outlined"
        OnClick="CancelData"
        Dense="true"
        Style="min-width: 120px; height: 40px; font-weight: 600;">
            @Localizer["Cancel"]
        </MudButton>
        <MudButton Color="Color.Primary"
        Variant="Variant.Filled"
        OnClick="SaveData"
        Dense="true"
        Style="min-width: 120px; height: 40px; font-weight: 600;">
            @Localizer["Save"]
        </MudButton>
    </div>

</div>
