using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class UnlicensedAccess
    {
        [Inject] private ActiveUser user { get; set; }
        protected override async Task OnInitializedAsync()
        {
            await Task.Delay(5000);
            NavigationManager.NavigateTo("/authentication/logout", forceLoad: true);
        }

        private void NavigateToContact()
        {
            NavigationManager.NavigateTo("/contact.html");
        }
    }
}