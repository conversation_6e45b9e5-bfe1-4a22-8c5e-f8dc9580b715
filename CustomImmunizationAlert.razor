﻿@page "/CustomImmunizationAlert"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using System
<div style="display: flex; flex-direction: column;">
    <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
        <MudGrid>
            <MudItem xs="8" Style="padding-right: 32px; border-right: 1px solid #E0E0E0;">
                <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">@Localizer["Custom Immunization Alerts"]</MudText>

                <SfGrid @ref="CustomImmunizationAlertGrid" TValue="CustomImmunizationAlerts" Style="font-size: 0.85rem; margin-top: 24px; width: 100%;"
                        DataSource="@customImmunizationAlerts" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="CustomImmunizationAlerts"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="Name" HeaderText="@Localizer["Name"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Description" HeaderText="@Localizer["Description"]" Width="200" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="WebReference" HeaderText="@Localizer["Web Reference"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="OrderSet" HeaderText="@Localizer["Order Set"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Gender" HeaderText="@Localizer["Gender"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="120" Format="MM/dd/y" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" Width="80" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </MudItem>

            <MudItem xs="4" Style="padding-left: 24px;">
                <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">@Localizer["Add New Immunization Alert"]</MudText>

                <!-- Add Source Selection -->
                <MudSelect T="string" Label="@Localizer["Select Source"]" @bind-Value="selectedSource"
                           Dense="true" Margin="Margin.Dense" Variant="Variant.Outlined"
                           Style="margin-bottom: 16px;">
                    <MudSelectItem T="string" Value="@("CDC")">@Localizer["CDC"]</MudSelectItem>
                    <MudSelectItem T="string" Value="@("FDB")">@Localizer["FDB"]</MudSelectItem>
                </MudSelect>

                <!-- Vaccine Search Box based on selected source -->
                <div style="margin-bottom: 16px;">
                    @if (selectedSource == "CDC")
                    {
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search Vaccine"]"
                                         @bind-Value="selectedVaccineName"
                                         SearchFunc="@SearchVaccinesData"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="true"
                                         Style="width: 100%;"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense" />
                    }
                    else if (selectedSource == "FDB")
                    {
                        <MudAutocomplete T="FDBVaccines"
                                         Label="@Localizer["Select Vaccine"]"
                                         SearchFunc="SearchFDBVaccine"
                                         ToStringFunc="@(vaccine => vaccine?.EVD_CVX_CD_DESC_SHORT)"
                                         Clearable="true"
                                         CoerceText="true"
                                         MinCharacters="2"
                                         ResetValueOnEmptyText="true"
                                         ValueChanged="OnFDBVaccineSelected"
                                         Value="FDBSelectedVaccine"
                                         Dense="true"
                                         Margin="Margin.Dense"
                                         Variant="Variant.Outlined"
                                         Style="width: 100%;" />
                    }
                </div>

                <div style="display: flex; flex-direction: column; gap: 16px;">
                    <MudTextField @bind-Value="alertDescription" Label="@Localizer["Description"]" Variant="Variant.Outlined" Margin="Margin.Dense" Lines="3" />
                    <MudTextField @bind-Value="webReference" Label="@Localizer["Web Reference"]" Variant="Variant.Outlined" Margin="Margin.Dense" />

                    <div style="display: flex; gap: 16px;">
                        <MudNumericField @bind-Value="ageLowerBound" Label="@Localizer["Age Lower Bound"]" Variant="Variant.Outlined" Margin="Margin.Dense" Min="0" Max="120" Style="flex: 1;" />
                        <MudNumericField @bind-Value="ageUpperBound" Label="@Localizer["Age Upper Bound"]" Variant="Variant.Outlined" Margin="Margin.Dense" Min="0" Max="120" Style="flex: 1;" />
                    </div>
                    @* <MudTextField @bind-Value="orderSet" Label="@Localizer["Order Set"]" Variant="Variant.Outlined" Margin="Margin.Dense" /> *@
                    <MudAutocomplete T="string"
                                     Value="@orderSet"
                                     SearchFunc="SearchOrderset"
                                     ToStringFunc="@( (orderset) => orderset)"
                                     Placeholder="@Localizer["Orderset"]"
                                     ValueChanged="@OnOrdersetSelected"
                                     Adornment="Adornment.End"
                                     CoerceText="true"
                                     Clearable="true"
                                     Dense="true"
                                     ResetValueOnEmptyText="true"
                                     Margin="Margin.Dense"
                                     Variant="Variant.Outlined">
                    </MudAutocomplete>
                    <MudSelect @bind-Value="gender" Label="@Localizer["Gender"]" Variant="Variant.Outlined" Margin="Margin.Dense">
                        <MudSelectItem Value="@("Other")">@Localizer["Other"]</MudSelectItem>
                        <MudSelectItem Value="@("Male")">@Localizer["Male"]</MudSelectItem>
                        <MudSelectItem Value="@("Female")">@Localizer["Female"]</MudSelectItem>
                    </MudSelect>

                    <MudButton Color="Color.Primary"
                               OnClick="@AddNewAlert"
                               Variant="Variant.Filled"
                               Style="height: 40px; margin-top: 16px;">
                        @Localizer["Add Alert"]
                    </MudButton>
                </div>
            </MudItem>
        </MudGrid>

        <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0; margin-top: 24px;">
            <MudButton Color="Color.Secondary"
                       Variant="Variant.Outlined"
                       OnClick="@CancelData"
                       Dense="true"
                       Style="min-width: 120px; height: 40px; font-weight: 600;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Color="Color.Primary"
                       Variant="Variant.Filled"
                       OnClick="@SaveData"
                       Dense="true"
                       Style="min-width: 120px; height: 40px; font-weight: 600;">
                @Localizer["Save"]
            </MudButton>
        </div>
    </div>
</div>

<style>
    ::deep .e-grid .e-headercell {
        border-right: 2px solid #c0c0c0 !important;
        border-bottom: 2px solid #c0c0c0 !important;
        padding: 8px !important;
        font-weight: bold;
    }

    ::deep .e-grid .e-rowcell {
        border-right: 2px solid #c0c0c0 !important;
        padding: 8px !important;
    }

    ::deep .e-grid .e-row {
        border-bottom: 2px solid #c0c0c0 !important;
    }

    ::deep .e-grid {
        border: 2px solid #c0c0c0 !important;
    }

        ::deep .e-grid .e-row:hover {
            background-color: #f5f5f5 !important;
        }
</style>