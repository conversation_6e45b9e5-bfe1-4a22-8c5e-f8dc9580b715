﻿@page "/SubjectiveMedicalHistory"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System
@using Syncfusion.Blazor.DropDowns
@* @inject IStringLocalizer<TeyaAIScribeResource> Localizer *@

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="Symbol">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                   OnClick="OpenNewDialogBox"
                                   Size="Size.Small" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_medicalhistory" Style="width: 80vw; max-width: 1100px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["MedicalHistory"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="4">

                        <MudAutocomplete T="string"
                                         Label="@Localizer["SearchICD"]"
                                         Value="ICDName"
                                         ValueChanged="OnICDNameChanged"
                                         SearchFunc="SearchICDCodes"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         MinCharacters="2"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewHistory"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 70px; height: 35px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
                <SfGrid @ref="MedicalGrid" TValue="MedicalHistoryDTO" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@medicalhistory" GridLines="GridLine.Both" AllowPaging="true" PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler"  OnActionBegin="ActionBeginHandler" TValue="MedicalHistoryDTO"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="MedicalHistoryID" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field=@nameof(MedicalHistoryDTO.CreatedDate) HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" Format="MM/dd/yyyy" Width="30">
                            <EditTemplate>
                                <SfDatePicker TValue="DateTime?"
                                              @bind-Value="@((context as MedicalHistoryDTO).CreatedDate)"
                                              Max="@DateTime.Today"
                                              Placeholder="Select date"
                                              ShowClearButton="false">
                                </SfDatePicker>
                            </EditTemplate>
                        </GridColumn>
                        <GridColumn Field="History" HeaderText="@Localizer["History/Reason"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center"></GridColumn>

                        <GridColumn HeaderText="@Localizer["Actions"]" Width="25" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>


