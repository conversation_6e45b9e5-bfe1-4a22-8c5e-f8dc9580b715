﻿using Microsoft.AspNetCore.Components;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class UpdateUser
    {
        [Parameter]
        public string? userId { get; set; }

        private UpdatedUserViewModel? user = null;
        private bool isLoading = true;

        [Inject]
        private ILogger<UpdateUser> Logger { get; set; } = default!;
        [Inject]
        private IMemberService memberService { get; set; } = default!;
        

        protected override async Task OnInitializedAsync()
        {
            if (!string.IsNullOrEmpty(userId))
            {
                await LoadUserDetails();
            }
        }

        private async Task LoadUserDetails()
        {
            try
            {
                isLoading = true;
                var userDetailsJson = await customAuthenticationService.GetFullUserAsync(userId);
                user = JsonSerializer.Deserialize<UpdatedUserViewModel>(userDetailsJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                });
            }
            catch (Exception ex)
            {
                user = null;
                Logger.LogError(ex, Localizer["Error"]);
            }
            finally
            {
                isLoading = false;
            }
        }

       
        private async Task SaveUserDetails()
        {
            try
            {
                if (user != null && !string.IsNullOrEmpty(userId))
                {
                    if (await customAuthenticationService.UpdateAsync(userId, user))
                    {
                        var member = new Member
                        {
                            Id = Guid.Parse(userId),
                            UserName = user.DisplayName,
                            Email = user.Mail,
                            FirstName = user.GivenName,
                            LastName = user.Surname,
                            PhoneNumber = user.MobilePhone,
                            Address = new Address
                            {
                                AddressLine1 = user.StreetAddress,
                                PostalCode = user.PostalCode,
                                State = user.State,
                                Country = user.Country,
                            },
                            Country = user.Country,
                            IsActive=true
                        };
                        await memberService.UpdateMemberByIdAsync(member.Id, member);
                        Logger.LogInformation(Localizer["Saved"]);
                        Navigation.NavigateTo("/usermanagement");
                    }
                    else
                    {
                        Logger.LogError(Localizer["Failed"]);
                    }
                }
                else
                {
                    Logger.LogError(Localizer["Error"]);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
            }
        }
    }
}
