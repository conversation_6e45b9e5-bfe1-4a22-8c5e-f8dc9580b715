﻿@page "/Security"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "SecurityAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@layout Admin
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel

<GenericCard Heading="@Localizer["OrganizationProductAccessHeading"]">

    @if (Organizations != null && Organizations.Any())
    {
        <MudText Typo="Typo.h6" Class="mt-4 ml-2">@Localizer["SelectOrganization"]</MudText>
        <MudGrid Class="mx-2 my-4">
            <MudItem xs="12" md="6">
                <MudAutocomplete T="Organization" 
                     @bind-Value="SelectedOrganization"
                     SearchFunc="@SearchOrganizations"
                     ToStringFunc="@(org => org?.OrganizationName ?? string.Empty)"
                     Label="@Localizer["SearchSelectOrganization"]" 
                     Variant="Variant.Outlined"
                     Dense="false"
                     Clearable="true"
                     ResetValueOnEmptyText="true"
                     CoerceText="false"
                     CoerceValue="false"
                     MaxItems="null"  
                     ShowProgressIndicator="true"
                     Class="w-full">
                    <ItemTemplate Context="org">
                        <MudText>@org.OrganizationName</MudText>
                    </ItemTemplate>
                </MudAutocomplete>
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex align-center">
                @if (SelectedOrganization != null)
                {
                    <MudChip T="string" Color="Color.Primary" Size="Size.Medium" Icon="@Icons.Material.Filled.Business">
                        @Localizer["Selected"]: @SelectedOrganization.OrganizationName
                    </MudChip>
                }
            </MudItem>
        </MudGrid>
    }

    @if (SelectedOrganization != null && Products != null)
    {
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.h6" Class="ml-2">@Localizer["ManageProductsFor"]: <b>@SelectedOrganization.OrganizationName</b></MudText>

        <MudGrid Class="mx-2 my-4">
            <MudItem xs="12" md="8">
                <MudSelect T="Guid?" 
                           @bind-Value="SelectedProductToAdd" 
                           Label="@Localizer["SelectProductToAdd"]"
                           Variant="Variant.Outlined"
                           Dense="true"
                           Class="w-full">
                    @if (AvailableProducts?.Any() == true)
                    {
                        @foreach (var product in AvailableProducts)
                        {
                            <MudSelectItem T="Guid?" Value="@(product.Id)">
                                @product.Name
                            </MudSelectItem>
                        }
                    }
                    else
                    {
                        <MudSelectItem T="Guid?" Value="null" Disabled="true">
                            @(AvailableProducts == null 
                                ? Localizer["LoadingProducts"] 
                                : Localizer["NoProductsAvailable"])
                        </MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="4" Class="d-flex align-center">
                @if (SelectedProductToAdd != null)
                {
                    <MudButton Variant="Variant.Filled"
                               StartIcon="@Icons.Material.Filled.Add"
                               Color="Color.Primary"
                               Size="Size.Medium"
                               OnClick="AddProductToOrganization"
                               Disabled="IsAdding">
                        @if (IsAdding)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                            <span class="ml-2">@Localizer["Adding"]</span>
                        }
                        else
                        {
                            <span>@Localizer["AddProduct"]</span>
                        }
                    </MudButton>
                }
            </MudItem>
        </MudGrid>

        <SfGrid @ref="ProductGrid" DataSource="@Products"
                AllowPaging="true"
                AllowSorting="true"
                Style="margin: 0 2%; padding: 10px;">
            <GridPageSettings PageSize="10" PageSizes="true" />
            <GridColumns>
                <GridColumn Field="@nameof(Product.Id)" HeaderText="ID" Visible="false" />
                <GridColumn Field="@nameof(Product.Name)" HeaderText="@Localizer["ProductName"]" TextAlign="TextAlign.Center" />
                <GridColumn Field="@nameof(Product.Description)" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Center" />
                <GridColumn Field="@nameof(Product.Byproduct)" HeaderText="@Localizer["Byproduct"]" TextAlign="TextAlign.Center" />
                <GridColumn HeaderText="@Localizer["Actions"]" Width="120" TextAlign="TextAlign.Center">
                    <Template>
                        <MudTooltip Text="@Localizer["RemoveProductAccess"]">
                            <MudButton Variant="Variant.Text"
                                       StartIcon="@Icons.Material.Filled.Delete"
                                       Color="Color.Error"
                                       Size="Size.Small"
                                       OnClick="@(() => RemoveProductFromOrganization((Product)context))"
                                       Disabled="@IsRemoving">
                                @if (IsRemoving)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                }
                            </MudButton>
                        </MudTooltip>
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    }

    @if (SelectedOrganization == null && (Organizations == null || !Organizations.Any()))
    {
        <MudAlert Severity="Severity.Info" Class="m-4">
            @Localizer["NoOrganizationsAvailable"]
        </MudAlert>
    }

    @if (SelectedOrganization != null && (Products == null || !Products.Any()))
    {
        <MudAlert Severity="Severity.Warning" Class="m-4">
            @Localizer["NoProductsFound"]
        </MudAlert>
    }
</GenericCard>
