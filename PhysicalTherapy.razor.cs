﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class PhysicalTherapy
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IPhysicalTherapyService _PhysicalTherapyService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public string ICDName { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __PhysicalTherapy;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrgID { get; set; }
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        public SfGrid<TeyaUIModels.Model.PhysicalTherapyData> PhysicalTherapyGrid { get; set; }

        private List<TeyaUIModels.Model.PhysicalTherapyData> AddList = new();
        private List<TeyaUIModels.Model.PhysicalTherapyData> _PhysicalTherapy { get; set; }
        private List<TeyaUIModels.Model.PhysicalTherapyData> deletePhysicalTherapylist { get; set; } = new List<TeyaUIModels.Model.PhysicalTherapyData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };
        [Inject] private ILogger<PhysicalTherapy> _logger { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent { get; set; }

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;
            activeUserOrganizationId = OrgId;
            Subscription = UserContext.ActiveUserSubscription;

            _PhysicalTherapy = await _PhysicalTherapyService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);

        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _PhysicalTherapy
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])} <br><strong>{Localizer["Therapy Assessment"]}:</strong> {s.TherapyAssessment} <br><strong>{Localizer["Physical Therapy Diagnosis"]}:</strong> {s.PhysicalTherapyDiagnosis} <br><strong>{Localizer["Disabilities"]}:</strong> {s.Disabilities}</p>"));
        }

        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;

            string dynamicContent = (_PhysicalTherapy != null && _PhysicalTherapy.Any())
                ? string.Join("<br>", _PhysicalTherapy
                    .OrderByDescending(s => s.CreatedDate)
                    .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])} <br><strong>{Localizer["Therapy Assessment"]}:</strong> {s.TherapyAssessment} <br><strong>{Localizer["Physical Therapy Diagnosis"]}:</strong> {s.PhysicalTherapyDiagnosis} <br><strong>{Localizer["Disabilities"]}:</strong> {s.Disabilities}</p>"))
                : string.Empty;

            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {dynamicContent}
    </div>";
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>

        private async Task OpenNewDialogBox()
        {
            await __PhysicalTherapy.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __PhysicalTherapy.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private CancellationTokenSource _searchICDCancellationTokenSource;

        protected async Task<IEnumerable<string>> SearchICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<string>();

            // Cancel previous search if still running
            _searchICDCancellationTokenSource?.Cancel();
            _searchICDCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchICDCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var results = await _ICDService.GetAllICDCodesBySearchTermAsync(searchTerm);

                return results
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search ICD codes");
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewDiagnosis()
        {
            var newDiagnosis = new TeyaUIModels.Model.PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrgID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                PhysicalTherapyDiagnosis = ICDName,
                IsActive = true,
            };

            AddList.Add(newDiagnosis);
            _PhysicalTherapy.Add(newDiagnosis);
            await PhysicalTherapyGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            ICDName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.PhysicalTherapyData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var delelePhysicalTherapy = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.PhysicalTherapyID == delelePhysicalTherapy.PhysicalTherapyID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    delelePhysicalTherapy.IsActive = false;
                    delelePhysicalTherapy.UpdatedDate = DateTime.Now;
                    deletePhysicalTherapylist.Add(delelePhysicalTherapy);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.PhysicalTherapyData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _PhysicalTherapyService.AddPhysicalTherapyAsync(AddList, OrgID, Subscription);
            }
            await _PhysicalTherapyService.UpdatePhysicalTherapyListAsync(_PhysicalTherapy, OrgID, Subscription);
            await _PhysicalTherapyService.UpdatePhysicalTherapyListAsync(deletePhysicalTherapylist, OrgID, Subscription);
            deletePhysicalTherapylist.Clear();
            AddList.Clear();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deletePhysicalTherapylist.Clear();
            AddList.Clear();
            _PhysicalTherapy = await _PhysicalTherapyService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}