﻿using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
using System.Numerics;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using static MudBlazor.CategoryTypes;
using static System.Net.WebRequestMethods;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIViewModels.ViewModels;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Config
    {
        private bool isLoading = true;
        private string? errorMessage = null;
        private string? username;
        private Guid selectedOrganizationId;
        private MudTextField<string> roleNameInput;
        private MudTextField<string> facilityNameInput;
        private List<string> Countries = new();
        private List<Organization> Organizations = new();
        private List<Organization> ActiveOrganization = new();
        private Organization activeOrganization = new();
        private readonly string adminRole = "Admin";
        private List<Role> ActiveRoles = new();
        private List<Role> NonAdminRoles = new();
        string NewFacilityName = string.Empty;
        string NewRoleName = string.Empty;
        private List<Facility> ActiveUserFacilities = new();
        private List<VisitType> VisitTypes = new();
        private SfGrid<Organization>? organizationGrid;
        private SfGrid<Facility>? facilityGrid;
        private SfGrid<Role>? roleGrid;
        private SfGrid<VisitType>? visitTypeGrid;
        private bool Subscription = false;
        private MudForm form;
        private Organization? selectedOrganization;

        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PageRoleMappingData> PageUrls = new();
        private SfGrid<PageRoleMappingData>? pageRoleMappingGrid;
        private Role? _selectedRole;
        private Role? selectedRole
        {
            get => _selectedRole;
            set
            {
                if (_selectedRole != value)
                {
                    _selectedRole = value;
                    _ = OnRoleChanged(value);
                }
            }
        }

        private Organization Organization { get; set; } = new Organization();
        private Role Role { get; set; } = new Role();
        private Facility Facility { get; set; } = new Facility();
        [Inject] private ICountryService CountryService { get; set; }
        [Inject] private ILogger<Config> Logger { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private IFacilityService FacilityService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IVisitTypeService VisitTypeService { get; set; }
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPagePathService PagePathService { get; set; }
        [Inject] private RoleMappingState RoleMappingState { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadMembersAsync();
            await GetActiveOrganization();
            await GetActiveUserFacilitiesList();
            await GetActiveRolesList();
            await LoadVisitTypes();
            NonAdminRoles = ActiveRoles.Where(r => r.RoleName != adminRole).ToList();
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(Organization.OrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == "Enterprise";

            Role.OrganizationID = ActiveOrganization.FirstOrDefault().OrganizationId;
            Facility.OrganizationId = ActiveOrganization.FirstOrDefault().OrganizationId;
            var countries = await CountryService.GetAllCountriesAsync();
            Countries = countries.Select(c => c.CountryName).ToList();

            Countries.Sort();
            StateHasChanged();
        }

        /// <summary>
        /// Loads the visit types for a given organization.
        /// </summary>
        /// <param name="selectedOrganizationId">The ID of the selected organization.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        private async Task LoadVisitTypes()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                string s = User.OrganizationName;
                selectedOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                VisitTypes = await VisitTypeService.GetVisitTypesByOrganizationIdAsync(selectedOrganizationId, Subscription);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingVisitTypes"], ex.Message);
            }
            finally
            {
                isLoading = false;
            }
        }

        /// <summary>
        /// Processes grid actions, including adding, editing, and deleting visit types.
        /// </summary>
        /// <param name="args">The event arguments containing details about the grid operation.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task OnGridAction(ActionEventArgs<VisitType> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (await VisitTypeService.DeleteVisitTypeAsync(selectedOrganizationId, args.Data.VisitName, Subscription))
                {
                    VisitTypes.Remove(args.Data);
                    await visitTypeGrid.Refresh();
                    StateHasChanged();
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Action.Equals("edit", StringComparison.OrdinalIgnoreCase))
                {
                    if (!string.IsNullOrWhiteSpace(args.Data.CPTCode) &&
                        await VisitTypeService.UpdateCptCodeAsync(selectedOrganizationId, args.Data.VisitName, args.Data.CPTCode,Subscription))
                    {
                        VisitTypes.FirstOrDefault(v => v.VisitName == args.Data.VisitName)!.CPTCode = args.Data.CPTCode;
                        await visitTypeGrid.Refresh();
                        StateHasChanged();
                    }
                    else await LoadVisitTypes();
                }
                else if (args.Action.Equals("add", StringComparison.OrdinalIgnoreCase))
                {
                    if (!string.IsNullOrWhiteSpace(args.Data.VisitName) && !string.IsNullOrWhiteSpace(args.Data.CPTCode))
                    {
                        var newVisit = new VisitType
                        {
                            ID = Guid.NewGuid(),
                            OrganizationId = selectedOrganizationId,
                            VisitName = args.Data.VisitName,
                            CPTCode = args.Data.CPTCode
                        };

                        if (await VisitTypeService.AddVisitTypeAsync(newVisit))
                        {
                            await visitTypeGrid.Refresh();
                            StateHasChanged();
                        }
                    }
                    else await LoadVisitTypes();
                }
            }
        }

        private async Task LoadMembersAsync()
        {
            try
            {
                var pagePaths = await PagePathService.GetPagePathsAsync();

                PageUrls = pagePaths.Select(p => new PageRoleMappingData
                {
                    PagePath = p.PagePathValue
                }).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading organizations: {ex.Message}");
            }
        }

        private async Task GetActiveOrganization()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                username = User.id;
                var activeUserOrganization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                ActiveOrganization = activeUserOrganization.ToList();
                Organization.OrganizationId = ActiveOrganization.FirstOrDefault().OrganizationId;
                Organization.OrganizationName = ActiveOrganization.FirstOrDefault().OrganizationName;
                Organization.Country = ActiveOrganization.FirstOrDefault().Country;
                Organization.Address = ActiveOrganization.FirstOrDefault().Address;
                Organization.ContactNumber = ActiveOrganization.FirstOrDefault().ContactNumber;
                Organization.Email = ActiveOrganization.FirstOrDefault().Email;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingOrganizations"], ex.Message);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task GetActiveRolesList()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var roles = await RoleService.GetAllRolesByOrgIdAsync(activeUserOrganizationId, Subscription);
                ActiveRoles = roles.Where(role => role.IsActive).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingRoles"], ex.Message);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task GetActiveUserFacilitiesList()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var facilities = await FacilityService.GetAllFacilitiesByOrgIdAsync(activeUserOrganizationId, Subscription);
                ActiveUserFacilities = facilities.ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingFacilities"], ex.Message);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task RefreshOrganizationsGridAsync()
        {
            try
            {
                await GetActiveOrganization();
                StateHasChanged();
                Organization.OrganizationId = Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing organizations grid: {Message}", ex.Message);
            }
        }

        private async Task SaveOrganizationForm()
        {
            try
            {
                if (Organization.OrganizationId != Guid.Empty)
                {
                    await OrganizationService.UpdateOrganizationByIdAsync(Organization.OrganizationId, Organization);
                    Logger.LogInformation($"Updated Organization: {Organization.OrganizationName}");
                }
                await RefreshOrganizationsGridAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingOrganizationData"]);
            }
        }


        private async Task CancelOrganizationForm()
        {
            Organization = new Organization();
            Logger.LogInformation("Organization form reset.");
            StateHasChanged();
        }

        private async Task ShowDeleteConfirmationForFacility()
        {
            bool? result = await DialogService.ShowMessageBox(
                Localizer["Confirm Delete"],
                Localizer["Are you sure you want to delete the selected facility?"],
                yesText: Localizer["Yes"],
                noText: Localizer["Cancel"]
            );

            if (result == true)
            {
                await DeleteSelectedFacilities();
            }
        }

        private async Task ShowDeleteConfirmationForRole()
        {
            bool? result = await DialogService.ShowMessageBox(
                Localizer["Confirm Delete"],
                Localizer["Are you sure you want to delete the selected role?"],
                yesText: Localizer["Yes"],
                noText: Localizer["Cancel"]
            );

            if (result == true)
            {
                await DeleteSelectedRoles();
            }
        }


        private async Task DeleteSelectedFacilities()
        {
            try
            {
                if (facilityGrid != null)
                {
                    var selectedFacilities = await facilityGrid.GetSelectedRecordsAsync();

                    if (selectedFacilities != null && selectedFacilities.Any())
                    {
                        foreach (var facility in selectedFacilities)
                        {
                            facility.UpdatedDate = DateTime.Now;
                            facility.UpdatedBy = Guid.Parse(username);
                            facility.IsActive = false;
                            await FacilityService.UpdateFacilityByIdAsync(facility.FacilityId, facility);
                            Logger.LogInformation($"Inactive Facility: {facility.FacilityName}");
                        }

                        await RefreshFacilitiesGridAsync();
                    }
                    else
                    {
                        Logger.LogWarning("No facilities selected for deletion.");
                    }
                }
                else
                {
                    Logger.LogError("Facility grid is not initialized.");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while deleting selected facilities.");
            }
        }

        private async Task DeleteSelectedRoles()
        {
            try
            {
                if (roleGrid != null)
                {
                    var selectedRoles = await roleGrid.GetSelectedRecordsAsync();

                    if (selectedRoles != null && selectedRoles.Any())
                    {
                        foreach (var role in selectedRoles)
                        {
                            role.IsActive = false;
                            role.UpdatedDate = DateTime.Now;
                            role.UpdatedBy = Guid.Parse(username);
                            await RoleService.UpdateRoleByIdAsync(role.RoleId, role);
                            Logger.LogInformation($"Inactive Role: {role.RoleName}");
                        }

                        await RefreshRolesGridAsync();
                        StateHasChanged();
                    }
                    else
                    {
                        Logger.LogWarning("No roles selected for deletion.");
                    }
                }
                else
                {
                    Logger.LogError("Role grid is not initialized.");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while deleting selected roles.");
            }
        }

        private async Task RefreshRolesGridAsync()
        {
            try
            {
                await GetActiveRolesList();
                StateHasChanged();
                Role.RoleId = Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing roles grid: {Message}", ex.Message);
            }
        }

        private async Task SaveRoleAsync()
        {
            NewRoleName = Role.RoleName;
            bool isDuplicate = ActiveRoles.Any(r => r.RoleName.Equals(NewRoleName.Trim(), StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                await DialogService.ShowMessageBox(
                    Localizer["Duplicate Role Name"],
                    Localizer["Please provide a unique role name."],
                    yesText: Localizer["OK"]
                );
                return;
            }

            await SaveRoleForm();
        }

        private async Task SaveRoleForm()
        {
            try
            {
                if (Role.RoleId == Guid.Empty)
                {
                    Role.RoleId = Guid.NewGuid();
                }
                var registeredRole = await RoleService.RegisterRoleAsync(Role);
                Logger.LogInformation(Localizer["RoleRegistered"], registeredRole.RoleName);
                await RefreshRolesGridAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingRoleData"]);
            }
        }

        private async Task CancelRoleForm()
        {
            Role = new Role();
            Role.OrganizationID = Organizations.FirstOrDefault()?.OrganizationId ?? Guid.Empty;
            Logger.LogInformation("Role form reset.");
            StateHasChanged();
            await roleNameInput.FocusAsync();
        }

        private async Task RefreshFacilitiesGridAsync()
        {
            try
            {
                await GetActiveUserFacilitiesList();
                StateHasChanged();
                Facility.FacilityId = Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing facilities grid: {Message}", ex.Message);
            }
        }

        private async Task SaveFacilityAsync()
        {
            NewFacilityName = Facility.FacilityName;
            bool isDuplicate = ActiveUserFacilities.Any(f => f.FacilityName.Equals(NewFacilityName.Trim(), StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                await DialogService.ShowMessageBox(
                    Localizer["Duplicate Facility Name"],
                    Localizer["Please provide a unique facility name."],
                    yesText: Localizer["OK"]
                );
                return;
            }

            await SaveFacilityForm();
        }

        private async Task SaveFacilityForm()
        {
            try
            {
                Facility.FacilityId = Guid.NewGuid();
                var registeredFacility = await FacilityService.RegisterFacilityAsync(Facility);
                Logger.LogInformation(Localizer["FacilityRegistered"], registeredFacility.FacilityName);
                await RefreshFacilitiesGridAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingFacilityData"]);
            }
        }

        private async Task CancelFacilityForm()
        {
            Facility = new Facility();
            Facility.OrganizationId = Organizations.FirstOrDefault()?.OrganizationId ?? Guid.Empty;
            Logger.LogInformation(Localizer["Facility form reset."]);
            StateHasChanged();
            await facilityNameInput.FocusAsync();
        }

        /// <summary>
        /// Handles the role change event and updates the page role mappings.
        /// </summary>
        /// <param name="role">The selected role.</param>
        private async Task OnRoleChanged(Role role)
        {
            selectedRole = role;
            activeOrganization = (await OrganizationService.GetOrganizationByIdAsync(selectedRole.OrganizationID));
            selectedOrganization = activeOrganization;
            if (selectedRole != null)
            {
                try
                {
                    PageRoleMappings.Clear();
                    var roleMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(selectedRole.RoleId, activeOrganization.OrganizationId, Subscription);
                    PageRoleMappings = roleMappings.ToList();
                    foreach (var page in PageUrls)
                    {
                        var mapping = roleMappings.FirstOrDefault(rm => rm.PagePath == page.PagePath);
                        page.HasAccess = mapping?.HasAccess ?? false;
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError("Error loading page role mappings: {ErrorMessage}", ex.Message);
                }
                finally
                {
                    if (PageRoleMappings.Count() == 0)
                    {
                        foreach (var page in PageUrls)
                        {
                            page.HasAccess = false;
                        }
                    }
                }
                await pageRoleMappingGrid.Refresh();
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles the checkbox change event and marks the page role mapping as modified.
        /// </summary>
        /// <param name="pageRoleMapping">The page role mapping data.</param>
        private void OnCheckboxChanged(PageRoleMappingData pageRoleMapping)
        {
            pageRoleMapping.IsModified = true;
        }

        /// <summary>
        /// Saves the selected page role mappings.
        /// </summary>
        private async Task SaveSelectedPageRoleMappings()
        {
            try
            {
                if (selectedRole == null)
                {
                    Logger.LogWarning("No role selected.");
                    return;
                }

                var modifiedPages = PageUrls.Where(page => page.IsModified).ToList();

                await SaveAccessChanges(modifiedPages);

                Logger.LogInformation("Page role mappings updated successfully.");
                await pageRoleMappingGrid.Refresh();
                RoleMappingState.NotifyStateChanged();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(Localizer["Error saving page role mappings: {ErrorMessage}"], ex.Message);
            }

        }

        /// <summary>
        /// Cancels the changes and reverts checkboxes to their original state.
        /// </summary>
        private void CancelChanges()
        {
            foreach (var page in PageUrls)
            {
                var originalMapping = PageRoleMappings.FirstOrDefault(rm => rm.PagePath == page.PagePath);
                page.HasAccess = originalMapping?.HasAccess ?? false;
                page.IsModified = false;
            }

            StateHasChanged();
        }


        /// <summary>
        /// Saves the access changes for the modified pages.
        /// </summary>
        /// <param name="modifiedPages">The list of modified pages.</param>
        private async Task SaveAccessChanges(List<PageRoleMappingData> modifiedPages)
        {
            if (selectedRole == null || selectedOrganization == null)
            {
                return;
            }

            try
            {
                foreach (var page in modifiedPages)
                {
                    var existingMapping = PageRoleMappings.FirstOrDefault(prm => prm.PagePath == page.PagePath && prm.RoleId == selectedRole.RoleId && prm.OrganizationID == selectedOrganization.OrganizationId);

                    if (existingMapping != null)
                    {
                        existingMapping.HasAccess = page.HasAccess;
                        existingMapping.UpdatedDate = DateTime.Now;
                        existingMapping.UpdatedBy = Guid.Parse(User.id);
                        await PageRoleMappingService.UpdatePageRoleMappingAsync(existingMapping);
                    }
                    else if (page.HasAccess)
                    {
                        var newMapping = new PageRoleMappingData
                        {
                            Id = Guid.NewGuid(),
                            PagePath = page.PagePath,
                            RoleId = selectedRole.RoleId,
                            RoleName = selectedRole.RoleName,
                            OrganizationID = selectedOrganization.OrganizationId,
                            CreatedBy = Guid.Parse(User.id),
                            CreatedDate = DateTime.Now,
                            IsActive = true,
                            HasAccess = page.HasAccess,
                            Subscription = Subscription
                        };

                        await PageRoleMappingService.AddPageRoleMappingAsync(newMapping);
                    }
                }

                PageRoleMappings = (await PageRoleMappingService.GetPageRoleMappingsAsync()).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError("Error Adding PageRoleMapping: {ErrorMessage}", ex.Message);
            }
        }
    }
}