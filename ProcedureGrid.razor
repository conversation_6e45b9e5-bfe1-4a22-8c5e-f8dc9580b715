﻿@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using MudBlazor

@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<MudGrid Spacing="3" Style="align-items: center; margin-bottom: 20px;">
    <MudItem xs="4">
        <MudAutocomplete T="CPT"
                         Label="@Localizer["Search CPT By Codes or Description"]"
                         Value="@selectedCPT"
                         ValueChanged="OnCPTSelected"
                         SearchFunc="SearchCPTCodes"
                         ToStringFunc="@(cpt => cpt != null ? $"{cpt.CPTCode} - {cpt.Description}" : "")"
                         CoerceText="true"
                         Clearable="true"
                         ResetValueOnEmptyText="true"
                         Variant="Variant.Outlined"
                         Margin="Margin.Dense"
                         MinCharacters="2"
                         Style="width: 100%;" />
    </MudItem>

    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
        <MudButton Color="Color.Primary"
                   OnClick="AddNewProcedure"
                   Variant="Variant.Filled"
                   Dense="true"
                   Style="min-width: 70px; height: 35px;">
            @Localizer["Add"]
        </MudButton>
    </MudItem>
</MudGrid>

<SfGrid @ref="ProcedureGrids"
        TValue="Procedures"
        Style="font-size: 0.85rem;"
        DataSource="@procedureRelatedToAssessments"
        AllowPaging="true"
        PageSettings-PageSize="5"
        AllowEditing="true"
        AllowSorting="true">
    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
    <GridPageSettings PageSize="10"></GridPageSettings>
    <GridEvents OnActionBegin="ActionBeginHandler" TValue="Procedures"></GridEvents>
    <GridColumns>
        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
        <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT"]" TextAlign="TextAlign.Center" Width="50" AllowEditing="false"></GridColumn>
        <GridColumn Field="Description" HeaderText=Description TextAlign="TextAlign.Center" Width="75" AllowEditing="false"></GridColumn>
        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="75"></GridColumn>
        <GridColumn Field="@nameof(Procedures.AssessmentData)"
                    HeaderText="@Localizer["RelatedAssessment"]"
                    TextAlign="TextAlign.Center"
                    Width="100"
                    EditTemplate="@AssessmentEditTemplate">
        </GridColumn>
        <GridColumn Field="OrderedBy" HeaderText="@Localizer["Ordered By"]" TextAlign="TextAlign.Center" Width="35" AllowEditing="false"></GridColumn>
        <GridColumn Field="OrderDate" HeaderText="@Localizer["Order Date"]" Width="40" TextAlign="TextAlign.Center" Format="MM/dd/yy"></GridColumn>

        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="30">
            <GridCommandColumns>
                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
            </GridCommandColumns>
        </GridColumn>
    </GridColumns>
</SfGrid>

<div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px;">
    <MudButton Color="Color.Secondary"
               Variant="Variant.Outlined"
               Dense="true"
               OnClick="CancelChanges"
               Style="min-width: 120px; height: 40px; font-weight: 600;">
        @Localizer["Cancel"]
    </MudButton>
    <MudButton Color="Color.Primary"
               Variant="Variant.Filled"
               OnClick="SaveChanges"
               Dense="true"
               Style="min-width: 120px; height: 40px; font-weight: 600;">
        @Localizer["Save"]
    </MudButton>
</div>

@code {
    
}