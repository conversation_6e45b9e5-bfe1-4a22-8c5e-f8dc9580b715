﻿@page "/Prescription"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" OnClick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_currentmedicdialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Prescription Medication"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <!-- Form Inputs Section -->
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="2" Style="align-items: center;">
                    <MudItem xs="3">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search Brand Names"]"
                                         Value="@drugName"
                                         ValueChanged="OnDrugNameChanged"
                                         SearchFunc="SearchBrandNames"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Margin="Margin.Dense"
                                         Variant="Variant.Outlined"
                                         MinCharacters="3"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudSelect T="string"
                                   Label="@Localizer["Dosage & InTake"]"
                                   @bind-Value="finalSBD"
                                   Dense="true"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Style="width: 100%;">
                            @foreach (var drugs in BrandSBD)
                            {
                                <MudSelectItem T="string" Value="@drugs">@drugs</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="3">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search Assessments"]"
                                         Value="@Diagnosis"
                                         ValueChanged="OnDiagnosisChanged"
                                         SearchFunc="SearchAssessments"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Margin="Margin.Dense"
                                         Variant="Variant.Outlined"
                                         MinCharacters="3"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewMedication"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 100px; height: 40px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- Medication Table -->
                <SfGrid @ref="MedicinesGrid"
                        TValue="ActiveMedication"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@medications"
                        AllowPaging="true"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="ActiveMedication"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="Assessments" HeaderText="@Localizer["Assessments"]" TextAlign="TextAlign.Center" Width="150" AllowEditing="false"></GridColumn>
                        <GridColumn Field="BrandName" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120" AllowEditing="false"></GridColumn>
                        <GridColumn Field="DrugDetails" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1"></GridColumn>
                        <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                        <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/y"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>


