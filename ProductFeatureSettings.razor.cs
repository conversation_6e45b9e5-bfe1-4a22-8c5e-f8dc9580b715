using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaWebApp.Services;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using Microsoft.Extensions.Logging;
using TeyaUIModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class ProductFeatureSettings
    {
        [Inject] private IProductFeatureService ProductFeatureService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        [Inject] private ILogger<ProductFeatureSettings> Logger { get; set; }
        [Inject] private ActiveUser user { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }

        private Guid ActiveUserOrganizationId;
        private List<Product> ProductList { get; set; } = new();
        private ProductRegistrationDto newProduct { get; set; } = new();
        private List<ProductRegistrationDto> NewProductRegistrationList { get; set; } = new();
        private List<ProductFeature> AllProductFeatures { get; set; } = new();
        private List<ProductFeature> FeaturesForSelectedProduct { get; set; } = new();
        private Product SelectedProduct { get; set; }
        private GridEditSettings ProductGridEditSettings { get; set; } = new() { AllowAdding = true, AllowEditing = true, AllowDeleting = true, AllowEditOnDblClick = false};
        private GridEditSettings FeatureGridEditSettings { get; set; } = new() { AllowAdding = true, AllowEditing = true, AllowDeleting = true, AllowEditOnDblClick = false };
        private SfGrid<Product> ProductGrid { get; set; }
        private SfGrid<ProductFeature> FeatureGrid { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                ProductList = new List<Product>();
                AllProductFeatures = new List<ProductFeature>();
                FeaturesForSelectedProduct = new List<ProductFeature>();
                SelectedProduct = null;
                ActiveUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);

                var products = await ProductService.GetProductsAsync();
                if (products != null)
                {
                    ProductList = products.ToList();
                }

                var features = await ProductFeatureService.GetAllProductFeaturesAsync();
                if (features != null)
                {
                    AllProductFeatures = features.ToList();
                }

                Logger.LogInformation("{PageName} page initialized successfully", nameof(ProductFeatureSettings));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing {PageName} page", nameof(ProductFeatureSettings));
            }
        }

        private async Task OnProductRowSelected(RowSelectEventArgs<Product> args)
        {
            await ShowProductFeatures(args.Data);
        }

        private async Task ShowProductFeatures(Product product)
        {
            if (product == null) return;

            try
            {
                SelectedProduct = product;
                FilterFeaturesForSelectedProduct();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error fetching features for product {product.Name}");
            }
        }

        private void FilterFeaturesForSelectedProduct()
        {
            if (SelectedProduct != null && AllProductFeatures != null)
            {
                FeaturesForSelectedProduct = AllProductFeatures
                    .Where(f => f.ProdId == SelectedProduct.Id)
                    .ToList();
            }
            else
            {
                FeaturesForSelectedProduct = new List<ProductFeature>();
            }
        }

        private async Task OnProductActionBegin(ActionEventArgs<Product> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    var product = args.Data;

                    // Duplicate validation
                    bool isDuplicate = ProductList.Any(p =>
                        p.Name?.Trim().Equals(product.Name?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        p.Id != product.Id);

                    if (isDuplicate)
                    {
                        Snackbar.Add(Localizer["Duplicate product name not allowed."], Severity.Error);
                        args.Cancel = true;
                        return;
                    }

                    if (product.Id == Guid.Empty)
                    {
                        product.Id = Guid.NewGuid();

                        var newProductRegistration = new ProductRegistrationDto
                        {
                            Id = product.Id,
                            Name = product.Name,
                            Description = product.Description,
                            ByProduct = product.Byproduct,
                            OrganizationId = ActiveUserOrganizationId
                        };

                        NewProductRegistrationList.Clear();
                        NewProductRegistrationList.Add(newProductRegistration);

                        await ProductService.RegisterProductsAsync(NewProductRegistrationList);
                        Logger.LogInformation($"Added product '{product.Name}'");
                    }
                    else
                    {
                        await ProductService.UpdateProductAsync(product.Id, product);
                        Logger.LogInformation($"Updated product '{product.Name}'");

                        if (SelectedProduct?.Id == product.Id)
                        {
                            SelectedProduct = product;
                            FilterFeaturesForSelectedProduct();
                        }
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    var product = args.Data;

                    // Confirmation dialog
                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["Confirm Delete"],
                        Localizer["Are you sure you want to delete this product?"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["No"]);

                    if (result != true)
                    {
                        args.Cancel = true;
                        return;
                    }

                    await ProductService.DeleteProductByIdAsync(product.Id, product.OrganizationId, product.Subscription);

                    if (SelectedProduct?.Id == product.Id)
                    {
                        SelectedProduct = null;
                        FeaturesForSelectedProduct = new List<ProductFeature>();
                    }

                    Logger.LogInformation($"Deleted product '{product.Name}'");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing product grid action");
            }
        }

        private async Task OnProductActionComplete(ActionEventArgs<Product> args)
        {
            Logger.LogInformation($"Product grid action complete: {args.RequestType}");

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save ||
                args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                try
                {
                    var products = await ProductService.GetProductsAsync();
                    if (products != null)
                    {
                        ProductList = products.ToList();
                        StateHasChanged();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error refreshing product list after grid operation");
                }
            }
        }

        private async Task OnFeatureActionBegin(ActionEventArgs<ProductFeature> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    var feature = args.Data;

                    // Duplicate validation
                    bool isDuplicate = AllProductFeatures.Any(f =>
                        f.FeatureName?.Trim().Equals(feature.FeatureName?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        f.ProdId == SelectedProduct?.Id &&
                        f.Id != feature.Id);

                    if (isDuplicate)
                    {
                        Snackbar.Add(Localizer["Duplicate feature name not allowed."], Severity.Error);
                        args.Cancel = true;
                        return;
                    }

                    if (feature.Id == Guid.Empty)
                    {
                        feature.Id = Guid.NewGuid();
                        feature.ProdId = SelectedProduct.Id;
                        feature.ProdName = SelectedProduct.Name;
                        feature.Created = DateTime.Now;
                        feature.Updated = DateTime.Now;
                        feature.Status = true;

                        Logger.LogInformation($"Adding new feature: ID={feature.Id}, Name={feature.FeatureName}");

                        await ProductFeatureService.AddProductFeatureAsync(feature);

                        if (!AllProductFeatures.Any(f => f.Id == feature.Id))
                        {
                            AllProductFeatures.Add(feature);
                        }

                        Logger.LogInformation($"Added feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");
                    }
                    else
                    {
                        feature.ProdId = SelectedProduct.Id;
                        feature.ProdName = SelectedProduct.Name;
                        feature.Updated = DateTime.Now;

                        Logger.LogInformation($"Updating existing feature: ID={feature.Id}, Name={feature.FeatureName}");

                        await ProductFeatureService.UpdateProductFeatureAsync(feature);

                        var index = AllProductFeatures.FindIndex(f => f.Id == feature.Id);
                        if (index >= 0)
                        {
                            AllProductFeatures[index] = feature;
                        }

                        Logger.LogInformation($"Updated feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");
                    }

                    FilterFeaturesForSelectedProduct();
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    var feature = args.Data;

                    // Confirmation dialog
                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["Confirm Delete"],
                        Localizer["Are you sure you want to delete this feature?"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["No"]);

                    if (result != true)
                    {
                        args.Cancel = true;
                        return;
                    }

                    await ProductFeatureService.DeleteProductFeatureByIdAsync(feature.Id);

                    AllProductFeatures.RemoveAll(f => f.Id == feature.Id);

                    Logger.LogInformation($"Deleted feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");

                    FilterFeaturesForSelectedProduct();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing feature grid action");
            }
        }

        private async Task OnFeatureActionComplete(ActionEventArgs<ProductFeature> args)
        {
            Logger.LogInformation($"Feature grid action complete: {args.RequestType}");

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save ||
                args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                try
                {
                    var features = await ProductFeatureService.GetAllProductFeaturesAsync();
                    if (features != null)
                    {
                        AllProductFeatures = features.ToList();
                        FilterFeaturesForSelectedProduct();
                        StateHasChanged();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error refreshing feature list after grid operation");
                }
            }
        }
    }
}