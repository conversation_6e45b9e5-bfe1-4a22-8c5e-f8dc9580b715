using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Models;
using Microsoft.JSInterop;
using MudBlazor;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Timers;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.TeyaAIScribeResources;
using TeyaWebApp.ViewModel;
using Unity;
using static Microsoft.Azure.Amqp.CbsConstants;

namespace TeyaWebApp.Components.Pages
{
    public partial class Notes : ComponentBase
    {
        private bool isLoading { get; set; }
        private Random random = new Random();
        private bool isPaused = false;
        private System.Threading.Timer animationTimer;
        private bool AICard { get; set; }
        private bool toggleCard = true;
        private bool productVisibility;
        private bool componentRefreshKey = false;   //test key for refresh
        private Guid currentRecordId { get; set; }
        private string currentPatientName { get; set; }
        private Guid currentPatientId { get; set; }
        private bool PatientCreationFlag { get; set; } = false;
        [Inject]
        private ILogger<Notes> logger { get; set; }
        private List<Record> records = new();
        private List<Member> AllOrgMembers = new();
        private List<Dictionary<string, Dictionary<string, string>>> NotesData = new List<Dictionary<string, Dictionary<string, string>>>();
        [Inject] ITemplateService templateService { get; set; }
        private HashSet<string> sectionNames = new(); // HashSet to prevent duplicates
        private HashSet<string> RenderedSections = new(); // Tracks rendered UI sections
        private List<TemplateData> ProviderData = new();

        private MudDialog MicrophoneDialog;
        private Type componentType { get; set; }
        [Inject]
        private ISoapNotesComponentsService SoapNotesComponentsService { get; set; }
        [Inject]
        private ILogger<Notes> Logger { get; set; }
        private IEnumerable<SoapNotesComponent> ListDetails = new List<SoapNotesComponent>();
        [Inject]
        private ActiveUser User { get; set; }
        private string data_value { get; set; } = String.Empty;
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        //[Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private Guid? OrgID { get; set; }

        private Dictionary<string, Dictionary<string, string>> parsedNotes;
        private bool hasChanges = false;
        [Inject] private ISnackbar Snackbar { get; set; }

        [Parameter] public EventCallback OnGoBackToEncounters { get; set; }
        [Parameter] public EventCallback OnClearSelectedNoteId { get; set; }
        [Parameter] public bool ShowSingleRecordView { get; set; }

        private bool NewRecord { get; set; }
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
        };
        private string editorContent;
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid? PatientID { get; set; }
        private string TemplateName { get; set; }
        private bool isAnimating = false;
        private bool isRecorderActive = false;
        private bool isPausedd = false;
        private IEnumerable<Speech> Speeches = new List<Speech>();
        private bool isDataLoading = true;

        private string? CurrentSpeechText { get; set; }
        private string? PatientId { get; set; }
        private string MicIcon => isRecorderActive ? Localizer["stop_circle"] : Localizer["mic"];
        private string PauseResumeIcon => isPausedd ? Localizer["play_arrow"] : Localizer["pause"];
        private System.Timers.Timer callDurationTimer;
        private int callDuration = 0;
        private bool isAudioDetected = false;
        public Member member = new Member();
        public TeyaUIModels.Model.Address address = new TeyaUIModels.Model.Address();
        private Insurance insurance = new Insurance();
        private Guardian guardian = new Guardian();
        private Employer employer = new Employer();
        private List<CompleteOrderSet> resolveOrderset;
        private Record record;
        [Inject] IJSRuntime JS { get; set; }
        [Inject] UserContext UserContext { get; set; }  

        [Parameter] public Guid selectedNoteId { get; set; }
        [Parameter] public EventCallback<Guid> OnSwitchToNotePage { get; set; }

        private bool showSingleRecordView = false;

        private readonly string[] speakerColorPalette = new[] {
        "#4285F4", // Blue
        "#EA4335", // Red
        "#34A853", // Green
        "#FBBC05", // Yellow
        "#8E24AA", // Purple
        "#00ACC1", // Cyan
        "#FB8C00", // Orange
        "#607D8B", // Blue Grey
        "#D81B60", // Pink
        "#1E88E5", // Light Blue
        "#43A047", // Light Green
        "#6D4C41"  // Brown
    };

        private string GetSpeakerColor(int index)
        {
            return speakerColorPalette[index % speakerColorPalette.Length];
        }
        private string GetBubbleColor(string color)
        {
            if (color.StartsWith("#"))
            {
                try
                {
                    return $"{color}33";
                }
                catch
                {
                    return "#f5f5f5";
                }
            }
            return "#f5f5f5";
        }
        protected override async Task OnInitializedAsync()
        {
            PatientCreationFlag = _PatientService.PatientData == null;
            productVisibility = true;   //check product visibility of Provider, Need to be done
            ListDetails = await SoapNotesComponentsService.GetAllDetailsAsync();
            OrgID = UserContext.ActiveUserOrganizationID;
            activeUserOrganizationId = UserContext.ActiveUserOrganizationID;
            Subscription = UserContext.ActiveUserSubscription;
            ProviderData = await templateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);

            AllOrgMembers = await MemberService.GetAllMembersAsync(OrgID ?? Guid.Empty, Subscription);

            if (_PatientService.PatientData != null)
            {
                PatientID = _PatientService.PatientData.Id;
                //defaultTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == _PatientService.VisitType);
                OrgID = _PatientService.PatientData.OrganizationID;
            }

            if (selectedNoteId != Guid.Empty)
            {
                var singleRecord = await ProgressNotesService.GetRecordByIdAsync(selectedNoteId, OrgID, Subscription);
                if (singleRecord != null)
                {
                    records = new List<Record> { singleRecord };
                    showSingleRecordView = true;
                }
                else
                {
                    Snackbar.Add(Localizer["RecordNotFound"], Severity.Error);
                    showSingleRecordView = false;
                }
            }

            else
            {
                if (_PatientService.PatientData != null)
                {
                    PatientID = _PatientService.PatientData.Id;
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(PatientID.Value,OrgID,Subscription);
                }
                else
                {
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID,Subscription);
                }
                records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();

            }
            await Task.Delay(100); 
            isDataLoading = false;

            record = records.FirstOrDefault();
        }

        public string CleanHtml(string rawHtml)
        {
            if (string.IsNullOrEmpty(rawHtml))
                return string.Empty;
            return Regex.Replace(rawHtml, @"<h4[^>]*>.*?<\/h4>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline, TimeSpan.FromSeconds(1));
        }
        public List<Dictionary<string, Dictionary<string, string>>> ExtractNotesData(string recordNotes)
        {
            if (string.IsNullOrEmpty(recordNotes))
                return new List<Dictionary<string, Dictionary<string, string>>>();

            try
            {
                // Clean the JSON string
                string cleanedJson = recordNotes
                    .Replace("record.Notes = ", "")
                    .Replace("}{", "},{")
                    .Trim();

                // Wrap in array if not already
                if (!cleanedJson.StartsWith("["))
                    cleanedJson = $"[{cleanedJson}]";

                // Direct deserialization using System.Text.Json
                return JsonSerializer.Deserialize<List<Dictionary<string, Dictionary<string, string>>>>(cleanedJson)
                       ?? new List<Dictionary<string, Dictionary<string, string>>>();
            }
            catch
            {
                return new List<Dictionary<string, Dictionary<string, string>>>();
            }
        }


        private string ConvertToHtml(string markdown)
        {
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            return Markdown.ToHtml(markdown, pipeline);
        }

        [JSInvokable]
        public async Task OnAudioDetected(bool isAudioDetect)
        {
            isAudioDetected = isAudioDetect;
            await InvokeAsync(StateHasChanged);
        }
        private string GetBarStyle(int index)
        {
            if (isPaused)
            {
                return "--height: 25px; --opacity: 0.5;";
            }

            if (!isAudioDetected)
            {
                return "--height: 25px;";
            }

            // Random height between 15 and 65 when audio is detected
            double height = random.Next(15, 75);
            return $"--height: {height}px;";
        }
        private async Task<bool> ToggleTeyaAIVisibility()
        {
            //bool access = await MemberService.HasProductAccess(Guid.Parse(User.id), Guid.Parse(Environment.GetEnvironmentVariable("ProductId"))); //add patient ID
            //return access;
            return true;
        }
        private async Task AddNewRecord()
        {
            try
            {
                // Get the selected template
                TemplateData selectedTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == _PatientService.VisitType);
                if (selectedTemplate == null)
                {
                    var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                    selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                    if (selectedTemplate == null)
                    {
                        Snackbar.Add(Localizer["TemplateNotFound"], Severity.Error);
                        return;
                    }
                }

                // Parse the template JSON
                var templateStructure = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, Dictionary<string, string>>>>(selectedTemplate.Template);

                // Transform template structure into record structure
                var recordNotes = new Dictionary<string, Dictionary<string, string>>();

                foreach (var section in templateStructure)
                {
                    var sectionName = section.Key;
                    var fields = section.Value;

                    var recordSection = new Dictionary<string, string>();

                    foreach (var field in fields)
                    {
                        // Initialize each field with empty content
                        recordSection[field.Key] = string.Empty;
                    }

                    recordNotes[sectionName] = recordSection;
                }

                // Create a new record with the transformed structure
                var newRecord = new Record
                {
                    Id = Guid.NewGuid(),
                    PatientName = _PatientService.PatientData?.Name ?? "New Patient",
                    PatientId = _PatientService.PatientData?.Id ?? Guid.Empty,
                    PCPId = Guid.Parse(User.id),
                    DateTime = DateTime.Now,
                    Notes = JsonSerializer.Serialize(recordNotes), // Use the transformed structure
                    isEditable = true,
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName),
                    Transcription = string.Empty,
                    WordTimings = null
                };

                // Save the new record
                var response = await ProgressNotesService.UploadRecordAsync(newRecord,OrgID,Subscription);
                if (response.IsSuccessStatusCode)
                {
                    Snackbar.Add(Localizer["RecordAddedSuccessfully"], Severity.Success);

                    // Refresh the records list

                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(_PatientService.PatientData.Id, OrgID, Subscription);
                    records = records.Where(record => record.isEditable)
                                   .OrderByDescending(record => record.DateTime)
                                   .ToList();
                    record = records.FirstOrDefault();

                    componentRefreshKey = !componentRefreshKey;
                    StateHasChanged();
                }
                else
                {
                    Snackbar.Add(Localizer["ErrorAddingRecord"], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"Error adding new record: {ex.Message}");
                Snackbar.Add(Localizer["ErrorAddingRecord"], Severity.Error);
            }
        }
        private string GetAudioUrl(Guid id)
        {
            return $"{Environment.GetEnvironmentVariable(Localizer["AudioUrl"])}/{id}.{Localizer["webm"]}";
        }

        private void HandleRichTextEditorChange(Dictionary<string, Dictionary<string, string>> data, string category, string noteKey, string newValue)
        {
            if (!data.ContainsKey(category))
            {
                data[category] = new Dictionary<string, string>();
            }
            data[category][noteKey] = newValue;
            hasChanges = true;
            StateHasChanged();
        }

        private string GetEditorContent(Record record, string sectionKey, string fieldKey)
        {
            try
            {
                var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                    ?? new Dictionary<string, Dictionary<string, string>>();

                if (notesDict.ContainsKey(sectionKey) && notesDict[sectionKey].ContainsKey(fieldKey))
                {
                    return notesDict[sectionKey][fieldKey];
                }
                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private async Task HandleRichTextChange(Record record, string sectionKey, string fieldKey, string newValue)
        {
            var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                ?? new Dictionary<string, Dictionary<string, string>>();

            if (!notesDict.ContainsKey(sectionKey))
                notesDict[sectionKey] = new Dictionary<string, string>();

            notesDict[sectionKey][fieldKey] = newValue;
            record.Notes = JsonSerializer.Serialize(notesDict);
            hasChanges = true;
            await InvokeAsync(StateHasChanged);
        }


        public bool IsReadOnly(Record record)
        {
            if (record.isEditable == true)
            {
                return false;
            }
            return true;
        }

      
        private async Task LockRecord(Record recorded)
        {
            bool confirmed = await JS.InvokeAsync<bool>("confirm",
                "Are you sure you want to lock this record? You won't be able to make any changes afterward.");

            if (confirmed)
            {
                recorded.isEditable = false;
                selectedNoteId = Guid.Empty;
                var response = await ProgressNotesService.SaveRecordAsync(recorded, OrgID, Subscription);
                records = await ProgressNotesService.GetRecordsByPatientIdAsync(recorded.PatientId, OrgID, Subscription);
                records = records.Where(rec => rec.isEditable).OrderByDescending(rec => rec.DateTime).ToList();
                record = records.FirstOrDefault();
                componentRefreshKey = !componentRefreshKey;
                StateHasChanged();
            }
        }

        private async Task SaveRecord(Record recorded)
        {
            var response = await ProgressNotesService.SaveRecordAsync(recorded, OrgID, Subscription);
            records = await ProgressNotesService.GetRecordsByPatientIdAsync(recorded.PatientId, OrgID, Subscription);
            records = records.Where(rec => rec.isEditable).OrderByDescending(rec => rec.DateTime).ToList();
            record = records.FirstOrDefault();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            StateHasChanged();
        }
        private async Task CreatePatient()
        {

            if (member.FirstName == null || member.FirstName == string.Empty || member.LastName == null || member.LastName == string.Empty || member.Email == null || member.Email == string.Empty || member.DateOfBirth == null || member.SSN == null || member.SSN == string.Empty)
            {
                Snackbar.Add(Localizer["Please fill in all the Fields"], Severity.Warning);
                return;
            }

            if (AllOrgMembers?.Any(m => m.Email?.Equals(member.Email, StringComparison.OrdinalIgnoreCase) == true) == true)
            {
                Logger.LogWarning(Localizer["EmailAlreadyExists"]);
                Snackbar.Add(Localizer["Email Already Exists"], Severity.Warning);
                return;
            }



            member.Id = Guid.NewGuid();
            member.IsActive = true;
            insurance.InsuranceId = Guid.NewGuid();
            var insuranceAdded = await InsuranceService.AddInsuranceAsync(insurance);
            if (!insuranceAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                return;
            }

            address.AddressId = Guid.NewGuid();
            var addressAdded = await AddressService.AddAddressAsync(address);
            if (!addressAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                return;
            }

            guardian.GuardianId = Guid.NewGuid();
            var GuardianAdded = await GuardianService.AddGuardianAsync(guardian);
            if (!GuardianAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                return;
            }

            employer.EmployerId = Guid.NewGuid();
            var employerAdded = await EmployerService.AddEmployerAsync(employer);
            if (!employerAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                return;
            }
            member.AddressId = address.AddressId;
            member.InsuranceId = insurance.InsuranceId;
            member.GuardianId = guardian.GuardianId;
            member.EmployerId = employer.EmployerId;
            member.RoleName = "Patient";
            member.OrganizationName = User.OrganizationName;
            member.OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var roles = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
            roles = roles.Where(role => role.RoleName == "Patient").ToList();
            member.RoleID = roles.FirstOrDefault()?.RoleId
                 ?? throw new Exception(Localizer["RoleNotFoundError"]);
            var registeredMember = await MemberService.RegisterMembersAsync(new List<Member> { member });


            var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
            TemplateData selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
            //TemplateData selectedTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == "Intervention Visit");
            var templateStructure = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, Dictionary<string, string>>>>(selectedTemplate.Template);

            // Transform template structure into record structure
            var recordNotes = new Dictionary<string, Dictionary<string, string>>();

            foreach (var section in templateStructure)
            {
                var sectionName = section.Key;
                var fields = section.Value;

                var recordSection = new Dictionary<string, string>();

                foreach (var field in fields)
                {
                    // Initialize each field with empty content
                    recordSection[field.Key] = string.Empty;
                }

                recordNotes[sectionName] = recordSection;
            }

            var newRecord = new Record
            {
                Id = Guid.NewGuid(),
                PatientName = member.FirstName,
                PatientId = member.Id,
                PCPId = Guid.Parse(User.id),
                DateTime = DateTime.Now,
                Notes = JsonSerializer.Serialize(recordNotes),
                isEditable = true,
                OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName),
                Transcription = string.Empty,
                WordTimings = null
            };

            // Save the new record
            await ProgressNotesService.UploadRecordAsync(newRecord, OrgID, Subscription);

            // Refresh the records list
            records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
            records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();
            record = records.FirstOrDefault();

            member.FirstName = string.Empty;
            member.LastName = string.Empty;
            member.Email = string.Empty;
            member.SSN = string.Empty;
            member.DateOfBirth = null;
            Snackbar.Add(Localizer["Record Created Successfully"], Severity.Success);
            componentRefreshKey = !componentRefreshKey;
            await InvokeAsync(StateHasChanged);


        }
        //private async Task LockRecord()
        //{
        //    records[0].isEditable = false;
        //    records[0].Notes = JsonSerializer.Serialize(parsedNotes);
        //    var response = await ProgressNotesService.SaveRecordAsync(records[0], OrgID, Subscription);
        //    if (response.IsSuccessStatusCode)
        //    {
        //        records[0] = null;
        //    }

            
        //}

        Type GetComponentType(string componentName)
        {
            string fullTypeName = $"TeyaWebApp.Components.Pages.{componentName}";
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetType(fullTypeName);

        }
        private void ShowMicrophone(Record record)
        {
            currentPatientId = record.PatientId;
            currentRecordId = record.Id;
            currentPatientName = record.PatientName;
            MicrophoneDialog.ShowAsync();
        }

        private async Task CloseMicrophoneDialog()
        {
            isRecorderActive = false;
            StopAnimation();
            StopCallDurationTimer();
            await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.CancelRecord");
            MicrophoneDialog.CloseAsync();
        }
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["Backdrop-Disabled"], Severity.Info);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Task.Delay(int.Parse(Localizer["500"]));
                var accessToken = TokenService.AccessToken;
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.Initialize"], DotNetObjectReference.Create(this));
            }
        }
        private async Task OnMicIconClick()
        {
            isRecorderActive = !isRecorderActive;

            if (isRecorderActive)
            {
                StartAnimation();
                StartCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StartRecord"]);
                await speechService.StartTranscriptionAsync(Guid.NewGuid());
            }
            else
            {
                isLoading = true; // Show loading icon
                StateHasChanged(); // Force UI update
                StopAnimation();
                StopCallDurationTimer();

                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StopRecord"], currentRecordId, TokenService.AccessToken);

                //Guid id = Guid.NewGuid();
                //await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StopRecord"], id, TokenService.AccessToken);
                if (_PatientService.PatientData != null)
                {


                    await speechService.StopTranscriptionAsync(currentRecordId, _PatientService.PatientData.Id,currentPatientName,_PatientService.VisitType, OrgID, Subscription);
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(_PatientService.PatientData.Id, OrgID, Subscription);
                   
                }
                else
                {
                    await speechService.StopTranscriptionAsync(currentRecordId, currentPatientId, currentPatientName, _PatientService.VisitType, OrgID,Subscription);
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID,Subscription);
                }
                records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();
                record = records.FirstOrDefault();
                AICard = true;
                //for (int i = 0; i < records.Count; i++)
                //{
                //    parsedNotes[i] = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(
                //    JsonSerializer.Serialize(JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records[i].Notes)));
                //}
                isLoading = false; // Hide loading icon
                CloseMicrophoneDialog();
            }
            componentRefreshKey=!componentRefreshKey;
            StateHasChanged();
        }

      
    


        protected override void OnInitialized()
        {
            animationTimer = new System.Threading.Timer(UpdateBars, null, 0, 100);
        }
        public async Task OpenTreatmentDialogAsync()
        {
            try
            {
                await DialogService.ShowAsync<TreatmentPage>("Treatment", new DialogOptions { MaxWidth = MaxWidth.ExtraLarge, FullWidth = true, CloseOnEscapeKey = true, CloseButton = true });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        private string GetRandomBarStyle()
        {
            if (isPaused)
            {
                int height = random.Next(15, 69);
                return $"--height: {height}px; --opacity: 0.5;";
            }

            int randomHeight = random.Next(32, 110);
            return $"--height: {randomHeight}px;";
        }
        private string GetNormalBarStyle()
        {
            int randomHeight = 25;
            return $"--height: {randomHeight}px;";
        }

        private void UpdateBars(object state)
        {
            if (!isPaused)
            {
                InvokeAsync(StateHasChanged);
            }
        }

        public void Dispose()
        {
            animationTimer?.Dispose();
        }
        [JSInvokable]
        public async Task OnRecordingComplete(string recordId)
        {
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnPauseIconClick()
        {
            isPaused = !isPaused;
            if (isPaused)
            {
                PauseAnimation();
                PauseCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.PauseRecord"]);
            }
            else
            {
                ResumeAnimation();
                ResumeCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.ResumeRecord"]);
            }

            StateHasChanged();
        }
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            await speechService.ProcessAudioChunk(base64AudioChunk);
        }
        private void StartAnimation()
        {
            isAnimating = true;
            isPaused = false;
            StateHasChanged();
        }

        private void StopAnimation()
        {
            isAnimating = false;
            StateHasChanged();
        }

        private void PauseAnimation()
        {
            isPaused = true;
            StopAnimation();
        }

        private void ResumeAnimation()
        {
            isPaused = false;
            StartAnimation();
        }

        private void StartCallDurationTimer()
        {
            callDuration = 0;
            callDurationTimer = new System.Timers.Timer(1000);
            callDurationTimer.Elapsed += UpdateCallDuration;
            callDurationTimer.Start();
        }

        private void UpdateCallDuration(object sender, ElapsedEventArgs e)
        {
            callDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void StopCallDurationTimer()
        {
            callDurationTimer?.Stop();
            callDurationTimer?.Dispose();
            callDuration = 0;
            StateHasChanged();
        }

        private string GetBarAnimationStyle(int index) =>
            isAnimating ? $"animation: wave-lg {new Random().NextDouble() * (0.7 - 0.2) + 0.2}s infinite ease-in-out alternate;" : "";

        private void PauseCallDurationTimer()
        {
            callDurationTimer?.Stop();
        }

        private void ResumeCallDurationTimer()
        {
            callDurationTimer?.Start();
        }

        private string FormatCallDuration(int durationInSeconds)
        {
            TimeSpan time = TimeSpan.FromSeconds(durationInSeconds);
            return time.ToString(@"mm\:ss");
        }

        public void OpenCustomImmunizationAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomImmunizationAlert>("Immunization Alerts", options);

                // The dialog will be closed by the CustomLabAlert component when Save or Cancel is clicked
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        
        private async Task GoBackToEncounters()
        {


            if (OnGoBackToEncounters.HasDelegate)
            {
                await OnGoBackToEncounters.InvokeAsync();
            }
            if (OnClearSelectedNoteId.HasDelegate)
            {
                await OnClearSelectedNoteId.InvokeAsync();
            }
            StateHasChanged();

        }

    }
}   
