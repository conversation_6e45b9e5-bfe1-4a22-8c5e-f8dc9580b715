﻿@namespace TeyaWebApp.Components

<MudCard Elevation="0" Class="mb-4">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="@HeaderTypo">@Title</MudText>
        </CardHeaderContent>
    </MudCardHeader>
    <MudCardContent>
        @if (IsEditing)
        {
            <MudTextField @bind-Value="@Content" Label="Edit Content" />
        }
        else
        {
            <MudText Typo="Typo.body2">@Content</MudText>
        }
    </MudCardContent>
</MudCard>

@code {
    [Parameter] public string Title { get; set; }
    [Parameter] public string Content { get; set; }
    [Parameter] public Typo HeaderTypo { get; set; } = Typo.subtitle1;
    [Parameter] public bool IsEditing { get; set; }
}