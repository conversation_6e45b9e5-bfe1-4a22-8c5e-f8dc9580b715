﻿@page "/OfficeVisit"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "VisitAccessPolicy")]
@using Microsoft.AspNetCore.Components
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Grids
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaUIModels.Model
@inject TeyaUIViewModels.ViewModel.IOfficeVisitService VisitService
@inject IMemberService MemberService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<PageTitle>@Localizer["Office Visits"]</PageTitle>

<div style="padding: 5px;">
    <SfGrid DataSource="@OfficeVisits" AllowPaging="true" GridLines="GridLine.Both" AllowTextWrap="true">
        <GridPageSettings PageSize="10" PageSizes="true"></GridPageSettings>
        <GridColumns>
            <GridColumn Field=@nameof(OfficeVisitModel.VisitType) HeaderText="@Localizer["Visit Type"]" Width="130" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.AppointmentTime) HeaderText="@Localizer["Appointment Time"]" Format="H:mm" Width="140" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn HeaderText="@Localizer["Patient Name"]" Width="120" TextAlign="TextAlign.Center">
                <Template>
                    @if (context is OfficeVisitModel visit && visit.PatientName != null)
                    {
                        <span style="text-decoration: none; color: blue; cursor: pointer;"
                              @onclick="() => RedirectToChart(visit.Id, visit.VisitStatus, visit.VisitType)">
                            @visit.PatientName
                        </span>
                    }
                    else
                    {
                        <span>N/A</span>
                    }
                </Template>
            </GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.PR) HeaderText="@Localizer["P/R"]" Width="100" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Reason) HeaderText="@Localizer["Reason"]" Width="100" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Notes) HeaderText="@Localizer["Notes"]" Width="100" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Sex) HeaderText="@Localizer["Sex"]" Width="90" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Dob) HeaderText="@Localizer["Date of Birth"]" Format="MMM dd, yyyy" Width="120" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.VisitStatus) HeaderText="@Localizer["Visit Status"]" Width="120" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.ArrivalTime) HeaderText="@Localizer["Arrival Time"]" Format="H:mm" Width="110" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Duration) HeaderText="@Localizer["Duration"]" Width="100" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.RoomNumber) HeaderText="@Localizer["Room Number"]" Width="130" TextAlign="TextAlign.Center"></GridColumn>
        </GridColumns>
    </SfGrid>
</div>
