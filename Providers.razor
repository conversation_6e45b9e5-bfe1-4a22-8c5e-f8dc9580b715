﻿@page "/Providers"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "ProvidersAccessPolicy")]
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaWebApp.ViewModel
@using TeyaUIViewModels.ViewModel
@inject IOrganizationService OrganizationService
@inject IUpToDateService UpToDateService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<Providers> Logger

<MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="activeTabIndex">
    <MudTabPanel Text="@Localizer["SignInTab"]">
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["SignInHeader"]</MudText>
        <MudPaper Class="pa-4" Style="width:50vw">
            <MudForm @ref="form">
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudTextField @bind-Value="FormData.UserName" Label="@Localizer["Username"]" Variant="Variant.Text" Required="true" />
                </div>
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudTextField @bind-Value="FormData.Password" Label="@Localizer["Password"]" Variant="Variant.Text" Required="true" InputType="InputType.Password" />
                </div>
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudTextField @bind-Value="FormData.ProviderName" Label="@Localizer["ProviderName"]" Variant="Variant.Text" Required="true" />
                </div>
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudTextField @bind-Value="FormData.SearchName" Label="@Localizer["SearchName"]" Variant="Variant.Text" Required="true" />
                </div>
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudTextField @bind-Value="FormData.URL" Label="@Localizer["URL"]" Variant="Variant.Text" Required="true" />
                </div>
                <div style="width: 20vw; margin-left: 5vw; margin-bottom: 15px;">
                    <MudAutocomplete T="string" Label="@Localizer["SearchOrganization"]" @bind-Value="_selectedOrganization"
                                     SearchFunc="SearchOrganizations" Clearable="true" ResetValueOnEmpty="true" />
                </div>
                <MudButton OnClick="HandleValidSubmit" Variant="Variant.Filled" Color="Color.Error" Size="Size.Small"
                           Style="margin-top: 15px; margin-left:35vw; width: 10vw;">
                    @Localizer["Save"]
                </MudButton>
            </MudForm>
        </MudPaper>
    </MudTabPanel>
    <MudTabPanel Text="@Localizer["UpToDateTab"]" Disabled="@isUpToDateTabDisabled">
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["UpToDateHeader"]</MudText>
        <MudText>
            @Localizer["UpToDateContentDescription"]
        </MudText>
    </MudTabPanel>
</MudTabs>