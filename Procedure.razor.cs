﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Syncfusion.Blazor.Buttons;
using Unity;
using Syncfusion.Blazor.Navigations;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using System.Text.RegularExpressions;
using System.Text;

namespace TeyaWebApp.Components.Pages
{
    public partial class Procedure
    {
        [Inject] private ICustomProcedureAlertService CustomProcedureAlertService { get; set; }
        [Inject] public IAlertService AlertService { get; set; }
        [Inject] public ICPTService _CPTService { get; set; }
        [Inject] private ILogger<Procedures> Logger { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] public IMemberService MemberService { get; set; }


        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; } // Added ChiefComplaintService
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] public SharedNotesService SharedNotesService { get; set; }
        [Inject] private ILogger<Procedure> _logger { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private SfRichTextEditor richTextEditor;

        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private MudDialog showBrowsePopup { get; set; }
        private SfGrid<Procedures> ProcedureGrid;
        private Patient _PatientData = new Patient();
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        private CPT selectedCPT;
        private Procedures selectedProcedure;
        private List<string> AssessmentDiagnosis = new List<string>();
        private List<string> chiefComplaints = new List<string>(); // Added for chief complaints
        private List<ChiefComplaintDTO> chiefComplaintData = new List<ChiefComplaintDTO>(); // Added for chief complaint data
        public string CPTName { get; set; }
        [Inject] IAssessmentsService assessmentsService { get; set; }
        private List<Procedures> procedure = new();
        private List<Procedures> addedProcedure = new();
        private List<Procedures> updatedProcedure = new();
        private List<Procedures> deletedProcedure = new();
        private List<AssessmentsData> Localdata = new();
        private List<CPT> _cptCodes { get; set; } = new List<CPT>();

        private bool add = false;
        private Guid PatientId { get; set; }
        private Guid Id { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

        public string? ManualContent { get; set; }

        private Guid? OrgID { get; set; }
        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };

        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            PatientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;

            Subscription = UserContext.ActiveUserSubscription;
            procedure = await ProcedureService.LoadProcedureAsync(PatientId, OrgID, Subscription);
            // Generate editor content immediately with available data
            richTextContent = GenerateRichTextContent(ManualContent);

            // Start Phase 2 in background without awaiting
            _ = LoadProcedureDataAsync();
        }

        private async Task LoadProcedureDataAsync()
        {
            try
            {
              
                var patient = await MemberService.GetMemberByIdAsync(PatientId, OrgID ?? Guid.Empty, false);
                _PatientData.DOB = patient.DateOfBirth;
                _PatientData.Sex = patient.SexualOrientation;
                _PatientData.Name = patient.FirstName;

                foreach (var proc in procedure)
                {
                    if (!string.IsNullOrEmpty(proc.ChiefComplaintId?.ToString()) && string.IsNullOrEmpty(proc.ChiefComplaint))
                    {
                        var complaint = chiefComplaintData.FirstOrDefault(c => c.Id == proc.ChiefComplaintId);
                        if (complaint != null)
                        {
                            proc.ChiefComplaint = complaint.Description;
                        }
                    }
                }

                Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
                chiefComplaintData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription)).ToList();
                chiefComplaints = chiefComplaintData.Select(c => c.Description).ToList();

                SharedNotesService.OnChange += UpdateAssessments;
                AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
                
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }
        private async Task LoadChiefComplaintDataAsync()
        {
            try
            {
                chiefComplaintData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription)).ToList();
                chiefComplaints = chiefComplaintData.Select(c => c.Description).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading chief complaint data");
            }
        }

        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();
        }

        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", process.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        process.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(process.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private CancellationTokenSource _searchCPTCancellationTokenSource;

        private async Task<IEnumerable<CPT>> SearchCPTCodes(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<CPT>();

            // Cancel previous search if still running
            _searchCPTCancellationTokenSource?.Cancel();
            _searchCPTCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine cancellation tokens
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchCPTCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with search term
                return await _CPTService.GetAllCPTCodesBySearchTermAsync(searchTerm);
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<CPT>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search CPT codes");
                return Enumerable.Empty<CPT>();
            }
        }

        private async void AddNewProcedure()
        {
            if (selectedCPT == null)
            {
                Snackbar.Add(Localizer["Please select a CPT code first"], Severity.Warning);
                return;
            }

            var newProcedure = new Procedures
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                
                OrganizationId = OrgID ?? Guid.Empty,
                PcpId = Guid.Parse(User.id),
                CPTCode = selectedCPT.CPTCode,
                OrderedBy = User.givenName + " " + User.surname,
                Description = selectedCPT.Description,
                Notes = string.Empty,
                OrderDate = DateTime.Now,
                CreatedByUserId = Guid.Parse(User.id),
                UpdatedByUserId = Guid.Parse(User.id),
                LastUpdatedDate = DateTime.Now,
                IsDeleted = true,
            };

            procedure.Add(newProcedure);
            addedProcedure.Add(newProcedure);
            ResetInputFields();
            await ProcedureGrid.Refresh();
        }

        public async Task ActionBeginHandler(ActionEventArgs<Procedures> args)
        {

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                

            }

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                var today = DateTime.Now.Date;

                if (args.Data.OrderDate > today)
                {
                    Snackbar.Add(Localizer["Validate.CreateDate"], Severity.Error);
                    args.Cancel = true;
                }
            }
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedProcedure.Add(args.Data);
                args.Data.IsDeleted = false;
            }
             if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedProcedure.Contains(args.Data) && !updatedProcedure.Contains(args.Data))
                {
                    args.Data.UpdatedByUserId = Guid.Parse(User.id);
                    args.Data.LastUpdatedDate = DateTime.Now;
                    updatedProcedure.Add(args.Data);
                }
            }
            
        }

        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaints);
            builder.AddAttribute(2, "Value", process.ChiefComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.ChiefComplaint = value;
                    var selectedComplaint = chiefComplaintData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        process.ChiefComplaintId = selectedComplaint.Id;
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        private async Task SaveChanges()
        {
            var alertsCheckList = new List<Procedures>(addedProcedure);
            try
            {
                if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }
                if (updatedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }

                if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.ChiefComplaint)))
                {
                    Snackbar.Add(Localizer["Chief Complaint is Blank"], Severity.Warning);
                    return;
                }

                var proceduresToCheck = new List<Procedures>();
                proceduresToCheck.AddRange(addedProcedure);
                proceduresToCheck.AddRange(updatedProcedure);

                // Check for unsafe procedures and show alerts in snackbar, but don't save to database yet
                bool hasUnsafeProcedures = await CheckProceduresForSafetyAlerts(alertsCheckList, showInSnackbarOnly: true);

                if (hasUnsafeProcedures)
                {
                    bool? confirmSave = await DialogService.ShowMessageBox(
                        Localizer["Safety Alert"],
                        Localizer["One or more procedures may not be safe with the selected chief complaints. Do you want to save anyway?"],
                        yesText: Localizer["Save Anyway"],
                        noText: Localizer["Cancel"]);

                    if (confirmSave != true)
                    {
                        return; // Don't save if user cancels
                    }
                }

                if (addedProcedure.Any())
                {
                    await ProcedureService.AddProcedureAsync(addedProcedure, OrgID, Subscription);
                    addedProcedure.Clear();
                }

                if (updatedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(updatedProcedure, OrgID, Subscription);
                    updatedProcedure.Clear();
                }

                if (deletedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(deletedProcedure, OrgID, Subscription);
                    deletedProcedure.Clear();
                }

                await LoadProcedureAsync();
                richTextContent = GenerateRichTextContent(ManualContent);
                await HandleDynamicComponentUpdate();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                await richTextEditor.RefreshUIAsync();

                CloseBrowsePopup();

                // Now save alerts to database since user confirmed to save
                if (hasUnsafeProcedures)
                {
                    await CheckProceduresForSafetyAlerts(alertsCheckList, showInSnackbarOnly: false);
                }

               
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        private async Task CancelChanges()
        {
            addedProcedure.Clear();
            updatedProcedure.Clear();
            deletedProcedure.Clear();
            await LoadProcedureAsync();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseBrowsePopup();
         
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task LoadProcedureAsync()
        {
            try
            {
                procedure = await ProcedureService.LoadProcedureAsync(PatientId, OrgID, Subscription);
             
                foreach (var proc in procedure)
                {
                    if (!string.IsNullOrEmpty(proc.ChiefComplaintId?.ToString()) && string.IsNullOrEmpty(proc.ChiefComplaint))
                    {
                        var complaint = chiefComplaintData.FirstOrDefault(c => c.Id == proc.ChiefComplaintId);
                        if (complaint != null)
                        {
                            proc.ChiefComplaint = complaint.Description;
                        }
                    }
                }





                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();
                if (ProcedureGrid != null)
                    await ProcedureGrid.Refresh();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading Procedure");
            }
        }



        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup.CloseAsync();
        }

        private async Task OnCPTSelected(CPT selected)
        {
            selectedCPT = selected;
            StateHasChanged();
        }

        private void ResetInputFields()
        {
            selectedCPT = null;
        }

        private async Task OpenBrowsePopup()
        {
            if (showBrowsePopup != null)
            {
                await showBrowsePopup.ShowAsync();
            }
            else
            {
                Logger.LogError("showBrowsePopup is null");
            }
        }

        private string GenerateRichTextContent(string Data)
        {
            Data ??= string.Empty;

            string procedureContent = procedure != null
                ? string.Join(" ", procedure.OrderByDescending(p => p.OrderDate)
                    .Select(p => $"<ul><li style='margin-left: 20px;'><b>{p.OrderDate:yyyy-MM-dd}</b> : " +
                                $"CPT - {p.CPTCode}, " +
                                $"Description - {p.Description}, " +
                                $"Notes - {p.Notes}, " +
                                $"Assessment - {p.AssessmentData}, " +
                                $"Ordered By - {p.OrderedBy}</li></ul>"))
                : string.Empty;

            return $@"<div>
        <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
        {Data}
        <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
        {procedureContent}
        </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            richTextContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            richTextContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(richTextContent);
            }

        }

        /// <summary>
        /// Checks procedures for potential safety concerns based on chief complaint and procedure description
        /// </summary>
        /// <param name="proceduresToCheck">List of procedures to evaluate</param>
        /// <returns>Boolean indicating whether any safety alerts were generated</returns>
        /// <summary>
        /// Modified CheckProceduresForSafetyAlerts method with showInSnackbarOnly parameter
        /// </summary>
        private async Task<bool> CheckProceduresForSafetyAlerts(List<Procedures> proceduresToCheck, bool showInSnackbarOnly = false)
        {
            if (proceduresToCheck == null || !proceduresToCheck.Any())
                return false;

            int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();
            HashSet<string> checkedProcedureCombinations = new HashSet<string>();

            var customProcedureAlerts = await GetCustomProcedureAlertsForProcedures(proceduresToCheck);

            if (customProcedureAlerts.Count > 0)
            {
                foreach (var customAlert in customProcedureAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured Procedure Alert",
                        Description = customAlert.Description ?? $"Custom alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}. See reference: {customAlert.WebReference}",
                        AdditionalInfo = $"Order Set: {customAlert.OrderSet}, Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    Snackbar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                Severity.Info,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.MedicalServices;
                                });
                }
            }

            foreach (var procedure in proceduresToCheck)
            {
                if (string.IsNullOrEmpty(procedure.ChiefComplaint) || string.IsNullOrEmpty(procedure.Description))
                    continue;

                string combinationKey = $"{procedure.Description}|{procedure.ChiefComplaint}";
                if (checkedProcedureCombinations.Contains(combinationKey))
                    continue;
                checkedProcedureCombinations.Add(combinationKey);

                var chiefComplaint = chiefComplaintData.FirstOrDefault(c => c.Id == procedure.ChiefComplaintId);
                if (chiefComplaint == null)
                    continue;

                bool isAppropriate = await CheckProcedureMatchesChiefComplaint(procedure.Description, procedure.ChiefComplaint);

                if (!isAppropriate)
                {
                    string safetyResponse = await GetProcedureSafetyEvaluation(procedure.Description, procedure.ChiefComplaint);

                    string severityLevel = ExtractSeverityLevel(safetyResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested Procedure Alert",
                        Description = $"The procedure '{procedure.Description}' may not be appropriate for chief complaint '{procedure.ChiefComplaint}'.",
                        Solution = $"Consider reviewing the procedure order or consulting with a specialist. {safetyResponse}",
                        AdditionalInfo = $"Procedure: {procedure.Description}, Chief Complaint: {procedure.ChiefComplaint}, Patient Age: {patientAge}, Gender: {patientGender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    Snackbar.Add($"[AI SUGGESTED ALERT] ({patientAge}y, {patientGender}): {procedure.Description} may not be appropriate for {procedure.ChiefComplaint}. Severity: {severityLevel}",
                                snackbarSeverity,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.SmartToy;
                                });
                }
            }

            // Only save alerts to database if showInSnackbarOnly is false
            if (alertsToAdd.Count > 0 && !showInSnackbarOnly)
            {
                try
                {
                    await AlertService.AddAlertsAsync(alertsToAdd, OrgID, false);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorAddingAlerts"]);
                }
            }

            return alertsToAdd.Count > 0;
        }

        private async Task<bool> CheckProcedureMatchesChiefComplaint(string procedureName, string chiefComplaint)
        {
            // Include patient data in the prompt
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";
            string prompt = $"Patient: {age}-year-old {gender}\n" +
                           $"Chief Complaint: '{chiefComplaint}'\n" +
                           $"Proposed Procedure: '{procedureName}'\n\n" +
                           $"Question: Could this procedure directly worsen, aggravate, or negatively affect the patient's chief complaint?\n\n" +
                           $"Consider:\n" +
                           $"- Would the procedure make the symptoms worse?\n" +
                           $"- Could the procedure cause complications that would worsen the complaint?\n" +
                           $"- Are there direct contraindications where this procedure would harm someone with this complaint?\n\n" +
                           $"Answer ONLY:\n" +
                           $"'WORSENS' if the procedure could make the chief complaint worse\n" +
                           $"'SAFE' if the procedure would not worsen the chief complaint";

            string response = await AskGptModel(prompt);

            // Return false (not safe) if procedure could worsen the complaint
            // Return true (safe) if procedure would not worsen the complaint
            bool couldWorsen = response.Trim().ToUpper().Contains("WORSENS");

            // Log for debugging
            Logger.LogInformation($"Procedure: {procedureName}, Chief Complaint: {chiefComplaint}, GPT Response: '{response.Trim()}', Could Worsen: {couldWorsen}");

            return !couldWorsen; // Return true if safe, false if could worsen
        }

        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }
        /// Gets a complete safety evaluation for a procedure with a specific chief complaint using the GPT model
        /// </summary>
        /// <param name="procedureDescription">Description of the procedure</param>
        /// <param name="chiefComplaint">Chief complaint</param>
        /// <returns>Safety alert with severity level and explanation in 2 lines</returns>
        private async Task<string> GetProcedureSafetyEvaluation(string procedureDescription, string chiefComplaint)
        {
            try
            {
                int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string gender = _PatientData.Sex ?? "Unknown";

                string prompt = $"Provide a detailed safety evaluation for procedure '{procedureDescription}' " +
                               $"in a {age}-year-old {gender} patient with chief complaint '{chiefComplaint}'. " +
                               $"This procedure has been flagged as potentially harmful. Explain: " +
                               $"1) How it could negatively affect the patient's condition " +
                               $"2) Severity level (Low/Medium/High) " +
                               $"3) Specific contraindications or risks " +
                               $"Format: 'Severity: [Level]. Risk: [Brief explanation of how it could worsen the complaint]. Recommendation: [Clinical guidance].'";

                return await MeasureService.AskGptAsync(
                    "You are evaluating a procedure that has been identified as potentially harmful for a patient's chief complaint. " +
                    "Focus on explaining the specific risks and contraindications. Be concise but thorough in your clinical assessment.",
                    prompt);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error calling GPT model for safety evaluation");
                return "Severity: Medium. Risk: Unable to evaluate specific contraindications - manual review required. Recommendation: Consult with specialist before proceeding.";
            }
        }

        private async Task<List<CustomProcedureAlerts>> GetCustomProcedureAlertsForProcedures(List<Procedures> proceduresToCheck)
        {
            try
            {
                var customProcedureAlerts = await CustomProcedureAlertService.GetActiveCustomProcedureAlertsByOrganizationIdAsync(Id, OrgID, false);
                if (customProcedureAlerts == null || customProcedureAlerts.Count == 0)
                    return new List<CustomProcedureAlerts>();

                int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<CustomProcedureAlerts>();

                var procedureDetails = new List<(string ProcedureName, string ChiefComplaint)>();
                foreach (var procedure in proceduresToCheck)
                {
                    if (string.IsNullOrEmpty(procedure.ChiefComplaint) || string.IsNullOrEmpty(procedure.Description))
                        continue;

                    var chiefComplaint = chiefComplaintData.FirstOrDefault(c => c.Id == procedure.ChiefComplaintId);
                    if (chiefComplaint == null)
                        continue;

                    procedureDetails.Add((procedure.Description, procedure.ChiefComplaint));
                }

                if (procedureDetails.Count == 0)
                    return matchingAlerts;

                var procedureNames = procedureDetails.Select(p => p.ProcedureName).ToList();

                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customProcedureAlerts.Count; i++)
                {
                    var alert = customProcedureAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                StringBuilder procedureComplaintInfo = new StringBuilder();
                foreach (var detail in procedureDetails)
                {
                    procedureComplaintInfo.AppendLine($"- {detail.ProcedureName} for {detail.ChiefComplaint}");
                }

                StringBuilder alertsSummary = new StringBuilder();
                for (int i = 0; i < customProcedureAlerts.Count; i++)
                {
                    var alert = customProcedureAlerts[i];
                    alertsSummary.AppendLine($"Alert {i + 1}: {alert.Name} - {alert.Description}");
                }

                string prompt = $"Determine which alerts apply to these procedure-complaint pairs:\n\n" +
                    $"Patient: {patientAge}y, {patientGender}\n" +
                    $"Procedure-Complaint Pairs:\n{procedureComplaintInfo}\n\n" +
                    $"Alerts:\n{alertsSummary}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";

                // Ask GPT
                string response = await AskGptModel(prompt);

                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customProcedureAlerts.Count)
                        {
                            matchingAlerts.Add(customProcedureAlerts[alertIndex - 1]);
                        }
                    }
                }

                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customProcedureAlerts)
                    {
                        bool procedureMatch = false;

                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.Name.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.Description.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.OrderSet.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch)
                        {
                            var procedureKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                            foreach (var procedure in procedureNames)
                            {
                                var words = procedure.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        procedureKeywords.Add(word);
                                }
                            }

                            var alertKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                            if (!string.IsNullOrEmpty(alert.Name))
                            {
                                var words = alert.Name.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.Description))
                            {
                                var words = alert.Description.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        alertKeywords.Add(word);
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.OrderSet))
                            {
                                var words = alert.OrderSet.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            foreach (var procedureKeyword in procedureKeywords)
                            {
                                if (alertKeywords.Contains(procedureKeyword))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (procedureMatch)
                        {
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                return new List<CustomProcedureAlerts>();
            }
        }
        private async Task<string> AskGptModel(string prompt)
        {
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string systemMessage = $"You are a medical safety assistant focused on identifying procedures that could worsen a patient's current complaint. " +
                                  $"The patient is {age} years old and {gender}. " +
                                  $"Your ONLY job is to determine if a procedure could make the patient's chief complaint worse. " +
                                  $"You are NOT evaluating medical appropriateness or whether the procedure is indicated. " +
                                  $"Focus solely on potential harm or worsening of the existing complaint. " +
                                  $"If a procedure is unrelated to the complaint, it should be considered SAFE (won't worsen). " +
                                  $"Only flag as WORSENS if there's a direct risk of making the complaint worse.";

            return await MeasureService.AskGptAsync(systemMessage, prompt);
        }
    }
}