﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Markdig.Helpers;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using TeyaUIModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class CustomProcedureAlert
    {
        [Inject] public ICustomProcedureAlertService CustomProcedureAlertService { get; set; }
        [Inject] private ILogger<CustomProcedureAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<CustomProcedureAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<CustomProcedureAlerts>? CustomProcedureAlertGrid;

        private List<CustomProcedureAlerts> customProcedureAlerts = new();
        private List<CustomProcedureAlerts> deleteAlertList = new();
        private List<CustomProcedureAlerts> addList = new();
        private List<CompleteOrderSet> OrdersetList;
        private List<CompleteOrderSet> FilteredOrderset;
        private CompleteOrderSet selectedOrderset;
        private bool showNoResultsMessage;
        // Form fields
        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        public enum UserRoles { Other }
        private string gender = UserRoles.Other.ToString();


        [Inject]
        private PatientService PatientService { get; set; } = default!;
        [Inject] IOrderSetService orderSetService { get; set; }
        private Guid PatientId { get; set; }
        private Guid Id { get; set; }
        private Guid? OrgID { get; set; }

        /// <summary>
        /// Initializes the component by retrieving patient data and loading existing alerts.
        /// Sets up patient ID and organization ID from the patient service and attempts to load
        /// alert data while handling any exceptions that may occur during retrieval.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Check if patient is selected
                if (PatientService.PatientData != null && PatientService.PatientData.Id != Guid.Empty)
                {
                    // Patient is selected - use patient's organization
                    PatientId = PatientService.PatientData.Id;
                    OrgID = PatientService.PatientData.OrganizationID;
                    _logger.LogInformation($"Patient selected, using patient organization: {OrgID}");
                }
                else
                {
                    // No patient selected - get organization by name and then get organization ID
                    var organization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                    if (organization != null && organization.Count > 0)
                    {
                        OrgID = organization[0].OrganizationId;
                        _logger.LogInformation($"No patient selected, using user organization: {OrgID}");
                    }
                    else
                    {
                        _logger.LogWarning($"No organization found with name: {User.OrganizationName}");
                        OrgID = null;
                    }
                }

                // Load order sets first
                OrdersetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

                // Then load alerts
                await LoadAlertsAsync();

                // Force UI update after everything is loaded
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving procedure alert data");
                Snackbar?.Add("Error loading procedure data. Please try again later.", Severity.Error);
            }
        }

        /// <summary>
        /// Loads custom procedure alerts for the current patient and organization.
        /// Retrieves active alerts from the service and fills the grid with empty rows if needed
        /// to maintain a minimum of 9 rows for display purposes.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task LoadAlertsAsync()
        {
            try
            {
                _logger.LogInformation("Loading all procedure alerts from database");
                var existingAlerts = await CustomProcedureAlertService.GetActiveCustomProcedureAlertsByOrganizationIdAsync(Id, OrgID, false);
                _logger.LogInformation($"Found {existingAlerts?.Count() ?? 0} total alerts in database");

                customProcedureAlerts = existingAlerts?.ToList() ?? new List<CustomProcedureAlerts>();

                // Add empty rows for UI display
                int emptyRowsNeeded = Math.Max(0, 9 - customProcedureAlerts.Count);
                if (emptyRowsNeeded > 0)
                {
                    customProcedureAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                        .Select(_ => new CustomProcedureAlerts
                        {
                            Name = string.Empty,
                            Description = string.Empty,
                            WebReference = string.Empty,
                            OrderSet = string.Empty,
                            Gender = string.Empty
                        }));
                }

                // Force grid refresh if it exists
                if (CustomProcedureAlertGrid != null)
                {
                    await CustomProcedureAlertGrid.Refresh();
                }

                // Trigger UI update
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadAlertsAsync");
                customProcedureAlerts = new List<CustomProcedureAlerts>();
                Snackbar?.Add("Error loading procedure alerts. Please try again later.", Severity.Error);
            }
        }

        /// <summary>
        /// Handles the completion of grid actions, particularly focusing on delete operations.
        /// When a row is deleted, either removes it from the add list if it was newly added,
        /// or marks it as inactive and adds it to the delete list for database update.
        /// </summary>
        /// <param name="args">Event arguments containing information about the completed action.</param>
        public void ActionCompletedHandler(ActionEventArgs<CustomProcedureAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;  // Set IsActive to false instead of actually deleting
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }
        private async Task<IEnumerable<string>> SearchOrderset(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                FilteredOrderset = OrdersetList;
                orderSet = null;
                showNoResultsMessage = false;
                StateHasChanged();
                var result = OrdersetList
                    .Select(temp => temp.orderSet.OrderSetName)
                    .Where(name => !string.IsNullOrEmpty(name))
                    .Distinct()
                    .ToList();
                return result;
            }
            else
            {
                var filtered = OrdersetList
                            .Where(temp => temp.orderSet.OrderSetName != null &&
                                       temp.orderSet.OrderSetName.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                            .Select(temp => temp.orderSet.OrderSetName)
                            .ToList();

                if (!filtered.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No orderset Found" };
                }

                showNoResultsMessage = false;
                return filtered;
            }
        }

        private void OnOrdersetSelected(string ordersetName)
        {
            orderSet = ordersetName;
            selectedOrderset = OrdersetList.FirstOrDefault(o => o.orderSet.OrderSetName == ordersetName);
            StateHasChanged();
        }
        /// <summary>
        /// Handles the beginning of grid actions, particularly save operations.
        /// Updates the UpdatedDate property of the alert being saved to the current date and time.
        /// </summary>
        /// <param name="args">Event arguments containing information about the action being initiated.</param>
        public async Task ActionBeginHandler(ActionEventArgs<CustomProcedureAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Saves all changes to the custom procedure alerts.
        /// Processes new alerts in the add list, deleted alerts in the delete list,
        /// and updates to existing alerts. Refreshes the display after saving,
        /// displays a success notification, and closes the dialog.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    // Set IsActive to true for all new alerts
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await CustomProcedureAlertService.AddCustomProcedureAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await CustomProcedureAlertService.UpdateCustomProcedureAlertsListAsync(deleteAlertList, OrgID, false);
                }

                // Update existing alerts
                var existingAlerts = customProcedureAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await CustomProcedureAlertService.UpdateCustomProcedureAlertsListAsync(existingAlerts, OrgID, false);
                }

                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["Procedure alerts saved successfully"], Severity.Success);

                // Close the dialog
                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving procedure alert data");
                Snackbar.Add(_localizer["Failed to save procedure alert records"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancels all pending changes, clears the add and delete lists,
        /// reloads alerts from the database, resets input fields,
        /// and closes the dialog.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            // Close the dialog
            MudDialog?.Close();
        }

        /// <summary>
        /// Resets all input fields in the alert form to their default values.
        /// Clears text fields and resets dropdown selections to prepare for new input.
        /// </summary>
        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = UserRoles.Other.ToString();
        }

        /// <summary>
        /// Adds a new custom procedure alert using the values from the input form.
        /// Validates that required fields are filled, finds or creates an empty row,
        /// populates it with form data, adds it to the addList for database insertion,
        /// refreshes the grid display, and resets the form.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task AddNewAlert()
        {
            try
            {
                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please enter alert name"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(alertDescription))
                {
                    Snackbar.Add(_localizer["Please add a Description"], Severity.Warning);
                    return;
                }
                if (!ageLowerBound.HasValue || !ageUpperBound.HasValue)
                {
                    Snackbar.Add(_localizer["Please select age lower and upper bound"], Severity.Warning);
                    return;
                }

                if (ageUpperBound.Value == 0)
                {
                    Snackbar.Add(_localizer["Age upper bound should be minimum 1"], Severity.Warning);
                    return;
                }

                var emptyRow = customProcedureAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new CustomProcedureAlerts();
                    customProcedureAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrdersetId = selectedOrderset?.orderSet?.Id ?? Guid.Empty;
                emptyRow.OrderSet = orderSet ?? string.Empty;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;  // Set IsActive to true for new alerts

                addList.Add(emptyRow);

                if (CustomProcedureAlertGrid != null)
                {
                    await CustomProcedureAlertGrid.Refresh();
                }

                ResetInputFields();

                Snackbar.Add(_localizer["Alert added successfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new procedure alert");
                Snackbar.Add(_localizer["Failed to add procedure alert"], Severity.Error);
            }
        }
    }
}