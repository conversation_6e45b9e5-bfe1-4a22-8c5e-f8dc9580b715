﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaWebApp.TeyaAIScribeResources;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Syncfusion.Blazor.Buttons;
using Syncfusion.Blazor;

namespace TeyaWebApp.Components.Pages
{
    public partial class HPI : ComponentBase
    {
        [Inject] private IHistoryOfPresentIllnessService _historyOfPresentIllnessService { get; set; }
        [Inject] private ISymptomsService _symptomsService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
       
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ILogger<HPI> _logger { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private UserContext UserContext { get; set; }

        private SfRichTextEditor RichTextEditor;
        private SfGrid<HistoryOfPresentIllness> HpiGrid;
        private MudDialog _hpiDialog;
        private string editorContent;
        private Symptoms symptom;
        private List<HistoryOfPresentIllness> hpiEntries { get; set; } = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> originalEntries { get; set; } = new List<HistoryOfPresentIllness>();
        private List<Symptoms> symptomSuggestions = new List<Symptoms>();

        // Separate lists for add, update, and delete
        private List<HistoryOfPresentIllness> addList = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> updateList = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> deleteList = new List<HistoryOfPresentIllness>();
        private List<Symptoms> addSymptomList = new List<Symptoms>();
        private CancellationTokenSource _searchCancellationTokenSource;

        private Guid? OrgID { get; set; }
        private Guid patientId { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }

        protected override async Task OnInitializedAsync()
        {
            patientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;
            
            Subscription = UserContext.ActiveUserSubscription;
            hpiEntries = await _historyOfPresentIllnessService.GetActiveHpiByPatientIdAsync(patientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);

            _ = LoadMedicationDataAsync();

        }
        private async Task LoadMedicationDataAsync()
        {
            try
            {
                originalEntries = hpiEntries.Select(e => CloneHpiEntry(e)).ToList();
                symptomSuggestions = (await _symptomsService.GetSymptomsAsync()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, Localizer["InitializationError"]);
            }
        }
        public async Task LoadHpiEntries()
        {
          
            hpiEntries = await _historyOfPresentIllnessService.GetActiveHpiByPatientIdAsync(patientId, OrgID, Subscription);
            originalEntries = hpiEntries.Select(e => CloneHpiEntry(e)).ToList();
            await InvokeAsync(StateHasChanged);
        }
        private async Task OnSymptomChanged(Symptoms value)
        {
            symptom = value;
            StateHasChanged();
        }
        private async Task<IEnumerable<Symptoms>> SearchSymptom(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<Symptoms>();

            // Cancel previous search if still running
            _searchCancellationTokenSource?.Cancel();
            _searchCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Add debounce delay (300ms)
                await Task.Delay(300, _searchCancellationTokenSource.Token);

                // Call service with the current search term
                return await _symptomsService.GetSymptomsBySearchTerm(searchTerm);
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<Symptoms>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to search medications");
                return Enumerable.Empty<Symptoms>();
            }
        }
        public async Task LoadSymptoms()
        {
            symptomSuggestions = (await _symptomsService.GetSymptomsAsync()).ToList();
            await InvokeAsync(StateHasChanged);
        }

        public HistoryOfPresentIllness CloneHpiEntry(HistoryOfPresentIllness entry)
        {
            return new HistoryOfPresentIllness
            {
                Id = entry.Id,
                PatientId = entry.PatientId,
                OrganizationId = entry.OrganizationId,
                PCPId = entry.PCPId,
                Symptoms = entry.Symptoms,
                Location = entry.Location,
                Severity = entry.Severity,
                StartDate = entry.StartDate,
                EndDate = entry.EndDate,
                Notes = entry.Notes,
                IsActive = entry.IsActive
            };
        }

        /// <summary>
        ///  adding a new entry
        /// </summary>
        /// <returns></returns>
        public async Task AddNewEntry()
        {
            if (string.IsNullOrEmpty(symptom.Symptom))
            {
                Snackbar.Add(Localizer["SymptomWarning"], Severity.Warning);
                return;
            }

            var symptomEntry = symptomSuggestions.FirstOrDefault(s => s.Symptom.Equals(symptom.Symptom, StringComparison.OrdinalIgnoreCase));

            if (symptomEntry == null)
            {
                symptomEntry = new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = symptom.Symptom
                };
                symptomSuggestions.Add(symptomEntry);
                addSymptomList.Add(symptomEntry);


            }

            var newEntry = new HistoryOfPresentIllness
            {
                Id = Guid.NewGuid(),
                PatientId = patientId,
                OrganizationId = OrgID ?? Guid.Empty,
                PCPId = Guid.Parse(User.id),
                Symptoms = symptomEntry.Symptom,
                Location = null,
                Severity = null,
                Duration=null,
                StartDate = null,
                EndDate = null,
                Notes = null,
                IsActive = true
            };

            addList.Add(newEntry);
            hpiEntries.Add(newEntry);
            await HpiGrid.Refresh();
            ResetInputFields();
        }

        public void ResetInputFields()
        {
            symptom = new();
        }

        public async Task ActionCompletedHandler(ActionEventArgs<HistoryOfPresentIllness> args)
        {
           
             if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                var existingEntry = originalEntries.FirstOrDefault(e => e.Id == args.Data.Id && e.PatientId == args.Data.PatientId);
                if (existingEntry != null)
                {
                    updateList.Add(args.Data);
                }
            }
        }


        public async Task ActionBeginHandler(ActionEventArgs<HistoryOfPresentIllness> args)
        {


            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                deleteList.Add(args.Data);
            }

            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Validate Start Date and End Date

                var today = DateTime.Today;

                if (args.Data.StartDate.HasValue || args.Data.EndDate.HasValue)
                {
                    if (args.Data.StartDate > today)
                    {
                        Snackbar.Add(@Localizer["Validation.StartDateFuture"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.EndDate < today)
                    {
                        Snackbar.Add(@Localizer["Validation.EndDatePast"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.EndDate < args.Data.StartDate)
                    {
                        Snackbar.Add(@Localizer["Validation.EndBeforeStart"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.StartDate.HasValue && args.Data.StartDate > today)
                    {
                        Snackbar.Add(@Localizer["Validation.StartDateFuture"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.EndDate.HasValue && args.Data.EndDate < today)
                    {
                        Snackbar.Add(@Localizer["Validation.EndDatePast"], Severity.Warning);
                        args.Cancel = true;
                    }
                }
                    // Validate Notes 
                    if (!string.IsNullOrEmpty(args.Data.Notes) && !IsAlphabeticWithSpaces(args.Data.Notes))
                    {
                        Snackbar.Add(@Localizer["Validation.NotesAlphaOnly"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }

                    // Validate Severity
                    if (!string.IsNullOrEmpty(args.Data.Severity) && !IsAlphabeticWithSpaces(args.Data.Severity))
                    {
                        Snackbar.Add(@Localizer["Validation.SeverityAlphaOnly"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }

                    // Validate Location
                    if (!string.IsNullOrEmpty(args.Data.Location) && !IsAlphabeticWithSpaces(args.Data.Location))
                    {
                        Snackbar.Add(@Localizer["Validation.LocationAlphaOnly"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }

                
            }
        }
        // Method to check if a string contains only alphabets and spaces
        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s]+$");
        }

        /// <summary>
        /// This will made permanent changes in the Database
        /// </summary>
        /// <returns></returns>
        public async Task SaveChanges()
        {
            try
            {
                // Add new symptoms to the database

                if (addSymptomList.Any())
                {
                    await _symptomsService.AddSymptomsAsync(addSymptomList);
                    symptomSuggestions.AddRange(addSymptomList);
                }


                if (addList.Any())
                {
                    await _historyOfPresentIllnessService.AddHpiAsync(addList, OrgID, Subscription);
                    addList.Clear();
                }
                if (updateList.Any())
                {
                    await _historyOfPresentIllnessService.UpdateHpiListAsync(updateList, OrgID, Subscription);
                    updateList.Clear();
                }

                if (deleteList.Any())
                {
                    await _historyOfPresentIllnessService.UpdateHpiListAsync(deleteList, OrgID, Subscription);
                    deleteList.Clear();
                }

                await LoadHpiEntries();
                editorContent = GenerateRichTextContent(ManualContent);
                await InvokeAsync(StateHasChanged);
                await HandleDynamicComponentUpdate();

                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to save changes: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack Trace: {ex.StackTrace}");

                Snackbar.Add(Localizer["Error.SaveFailed"], Severity.Error);

            }
            CloseHpiDialog();
        }

        /// <summary>
        /// Cancel changes will discard all the changes that are not saved and go back to its previous state
        /// </summary>
        /// <returns></returns>
        public async Task CancelChanges()
        {
            hpiEntries = originalEntries.Select(e => CloneHpiEntry(e)).ToList();
            addList.Clear();
            updateList.Clear();
            deleteList.Clear();
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseHpiDialog();
        }


        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        public void OpenHpiDialog()
        {
            _hpiDialog.ShowAsync();
        }

        public void CloseHpiDialog()
        {
            ResetInputFields();
            _hpiDialog.CloseAsync();
        }

        public List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add" },
        };
        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;
            string hpiContent = hpiEntries != null
                ? string.Join(" ", hpiEntries
                    .Where(c => !string.IsNullOrEmpty(c.Symptoms))
                    .Select(c => $"<ul><li style='margin-left: 20px;'><b>{c.Symptoms}</b> : {c.Notes}</li></ul>"))
                : string.Empty;
            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {hpiContent}
    </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}
