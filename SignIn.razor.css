﻿/* General Styling */
body {
    font-family: Arial, sans-serif;
    background-color: rgb(0, 59, 92);
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.container {
    background-color: #fff;
    width: 1000px;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

h3 {
    text-align: center;
    color: rgb(0, 59, 92);
    margin-bottom: 20px;
}

.logo-container {
    display: flex;
    justify-content: center; /* Center the logo horizontally */
    margin-bottom: 20px; /* Space between logo and form */
}

.logo-img {
    height: 50px; /* Adjust the height as needed */
}

form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

label {
    color: rgb(0, 59, 92);
    font-weight: bold;
}

input[type="email"],
input[type="password"] {
    padding: 10px;
    border: 1px solid rgb(0, 59, 92);
    border-radius: 4px;
    font-size: 16px;
}

button {
    padding: 12px;
    background-color: rgb(0, 59, 92);
    color: white;
    font-size: 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

    button:hover {
        background-color: rgb(0, 70, 110);
    }

/* Links */
.auth-links {
    text-align: center;
    margin-top: 15px;
}

    .auth-links a {
        color: rgb(0, 59, 92);
        text-decoration: none;
    }

        .auth-links a:hover {
            text-decoration: underline;
        }

/* Media Queries for Mobile */
@media (max-width: 480px) {
    .container {
        width: 100%;
        padding: 20px;
        margin: 0 10px;
    }

    .logo-img {
        height: 40px; /* Adjust size for smaller screens */
    }
}
