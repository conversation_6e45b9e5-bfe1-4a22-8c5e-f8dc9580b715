﻿@page "/cdss/create-alert"
@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using System.Threading
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService

<MudCard Elevation="0" Class="mb-1">
    <MudCardHeader>
        <MudText Typo="Typo.h6">Patient Specific Alert</MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Class="ml-auto" OnClick="GoToCdss" />
    </MudCardHeader>
    <MudCardContent>
        <MudForm @ref="_form">

            <MudPaper Class="pa-4 mb-4" Elevation="1" Style="border:1px solid #ccc; border-radius:10px;">
                <MudText Typo="Typo.subtitle1" Class="mb-2">Alert Details</MudText>

                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudPaper Class="pa-3" Elevation="0" Style="border:1px solid #ddd; border-radius:10px;">
                            <MudText Typo="Typo.subtitle2" Class="mb-2">General Information</MudText>

                            <MudTextField Label="Alert Name" @bind-Value="_alert.AlertName" Required="true" Class="mb-3" />

                            <MudSelect @bind-Value="_alert.AlertType" Label="Alert Type" Required="true" RequiredError="Alert type is required">
                                @foreach (var type in Enum.GetValues(typeof(AlertType)))
                                {
                                    <MudSelectItem Value="@((AlertType)type)">@type.ToString()</MudSelectItem>
                                }
                            </MudSelect>

                            <MudTextField Label="Orders" @bind-Value="_alert.Orders" Class="mb-3" />
                            <MudTextField Label="Frequency" @bind-Value="_alert.Frequency" Class="mb-3" />
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudPaper Class="pa-3" Elevation="0" Style="border:1px solid #ddd; border-radius:10px;">
                            <MudText Typo="Typo.subtitle2" Class="mb-2">Timing & Recurrence</MudText>

                            <MudDatePicker Label="Last Done"
                                           @bind-Date="_alert.LastDone"
                                           DateFormat="MM/dd/yyyy"
                                           Class="mb-3"
                                           MaxDate="DateTime.Today" />

                            <MudDatePicker Label="Due Date"
                                           @bind-Date="_alert.DueDate"
                                           DateFormat="MM/dd/yyyy"
                                           Class="mb-3"
                                           MinDate="_alert.LastDone == default ? DateTime.Today : _alert.LastDone" />
                            <MudCheckBox T="bool" @bind-Checked="_alert.IsRecurring" Label="Recurring" Color="Color.Primary" Class="mb-3" />

                            <MudTextField T="int?" Label="Recall After" @bind-Value="_alert.RecallAfterDays"
                                          Adornment="Adornment.End" AdornmentText="Days" Class="mb-3" />
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <MudPaper Class="pa-4 mb-4" Elevation="1" Style="border:1px solid #ccc; border-radius:10px;">
                <MudText Typo="Typo.subtitle1" Class="mb-2">Additional Entries</MudText>

                <MudButton Variant="Variant.Outlined"
                           EndIcon="@Icons.Material.Filled.ArrowDropDown"
                           OnClick="@ToggleProvidersList"
                           Class="mb-3">
                    Providers List
                </MudButton>

                @if (_showProvidersList)
                {
                    <MudPaper Elevation="0" Class="pa-2 mb-3" Style="border:1px dashed #bbb; border-radius:8px;">
                        <MudText Typo="Typo.subtitle1" Class="mb-2">Provider Selection</MudText>
                        <MudSelect T="string" Label="Provider Name" @bind-Value="_providerAlertName" Class="mb-3">
                            @foreach (var provider in _providerList)
                            {
                                <MudSelectItem Value="@provider">@provider</MudSelectItem>
                            }
                        </MudSelect>
                    </MudPaper>
                }
            </MudPaper>

            <MudGrid Justify="Justify.FlexEnd">
                <MudItem>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SubmitForm">Save</MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="GoToCdss" Class="ml-2">Cancel</MudButton>
                </MudItem>
            </MudGrid>
        </MudForm>
    </MudCardContent>
</MudCard>
