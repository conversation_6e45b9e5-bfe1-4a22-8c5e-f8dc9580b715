﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;


namespace TeyaWebApp.Components.Pages
{
    public partial class ReferralOutgoing : Microsoft.AspNetCore.Components.ComponentBase
    {

        private Guid PatientId { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] IReferralOutgoingService _ReferralOutgoingService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        private MudDialog _ReferralOutgoingdialog;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };
        private List<string> ToolbarItems = new List<string> { "Add" };
        public string temperature { get; set; }
        public string weight { get; set; }
        public string height { get; set; }
        public string pulse { get; set; }
        public string blood_Pressure { get; set; }
        public SfGrid<PatientReferralOutgoing> ReferralOutgoingGrid { get; set; }
        private List<PatientReferralOutgoing> ReferralOutgoings { get; set; }
        private List<PatientReferralOutgoing> AddList = new();
        private List<PatientReferralOutgoing> DeleteList = new();
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent { get; set; }


        /// <summary>
        /// Open Edit Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _ReferralOutgoingdialog.ShowAsync();
        }

        /// <summary>
        /// Close Edit Dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _ReferralOutgoingdialog.CloseAsync();
        }
        bool add = false;

        /// <summary>
        /// Retrieve ReferralOutgoing and set rich text editor
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            Subscription = UserContext.ActiveUserSubscription;
            ReferralOutgoings = await _ReferralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);

        }

        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;

            string dynamicContent = (ReferralOutgoings != null && ReferralOutgoings.Any())
                ? string.Join("<p>",
                    ReferralOutgoings.OrderByDescending(v => v.CreatedDate)
                    .Select(display => $"<b>{display.CreatedDate.ToString("MM-dd-yyyy")}</b>&emsp;{display.ReferralReason}"))
                : string.Empty;

            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {dynamicContent}
    </div>";
        }

        /// <summary>
        /// Event Handler for add,edit,delete
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(ActionEventArgs<PatientReferralOutgoing> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedReferralOutgoing = args.Data as PatientReferralOutgoing;
                    var existingItem = AddList.FirstOrDefault(v => v.PlanReferralId == deletedReferralOutgoing.PlanReferralId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedReferralOutgoing.isActive = false;
                        deletedReferralOutgoing.UpdatedBy = Guid.Parse(User.id);
                        deletedReferralOutgoing.UpdatedDate = DateTime.Now;
                        DeleteList.Add(deletedReferralOutgoing);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.PlanReferralId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = OrgID;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedReferralOutgoing = args.Data;
                        if (addedReferralOutgoing != null)
                        {
                            AddList.Add(addedReferralOutgoing); 
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<PatientReferralOutgoing> args)
        {

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                var today = DateTime.Now.Date;

                if (args.Data.CreatedDate > today)
                {
                    SnackBar.Add(Localizer["Validate.CreateDate"], Severity.Error);
                    args.Cancel = true;
                }
            }

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        /// Handle BackdropClick
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await _ReferralOutgoingService.AddReferralOutgoingAsync(AddList, OrgID, Subscription);
            }
            await _ReferralOutgoingService.UpdateReferralOutgoingsListAsync(DeleteList, OrgID, Subscription);
            await _ReferralOutgoingService.UpdateReferralOutgoingsListAsync(ReferralOutgoings, OrgID, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
            SnackBar.Add(Localizer["RecordSaved"], Severity.Success);
        }

        /// <summary>
        /// Undo changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {

            DeleteList.Clear();
            AddList.Clear();
            ReferralOutgoings = await _ReferralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
            SnackBar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}