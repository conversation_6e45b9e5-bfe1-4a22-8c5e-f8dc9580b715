﻿@page "/patients"
@page "/patients/{memberId:guid?}"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "patientsAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using MudBlazor.Extensions
@layout Admin
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject IMemberService MemberService
@inject IAddressService AddressService
@inject IInsuranceService InsuranceService
@inject IGuardianService GuardianService
@inject IEmployerService EmployerService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject InviteMailParametersService inviteMailParametersService
@inject RazorComponentRenderer RazorRenderer
<MudContainer MaxWidth="MaxWidth.Medium" Class="mud-container-center">
    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleMemberForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["MemberDetails"]
        </MudButton>
    </div>
    @if (showMemberForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper custom-form-container">
            <div class="custom-form-row">
                <div class="d-flex flex-column">
                    @if (string.IsNullOrEmpty(member.ProfileImageUrl))
                    {
                        <div>
                            <MudFileUpload T="IBrowserFile" FilesChanged="@(file => HandleFileUploadWrapper(file))">
                                <ActivatorContent>
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.CloudUpload"
                                               id="uploadButton"
                                               Class="upload-button">
                                        @Localizer["UploadPatientPhoto"]
                                    </MudButton>
                                </ActivatorContent>
                            </MudFileUpload>
                            @if (!string.IsNullOrEmpty(uploadedFileName))
                            {
                                <div class="mt-1 text-caption">@uploadedFileName</div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="d-flex align-items-center">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Success"
                                       StartIcon="@Icons.Material.Filled.CheckCircle"
                                       DisableElevation="true"
                                       Class="upload-button mr-1">
                                @Localizer["PatientPhotoUploaded"]
                            </MudButton>
                            <MudTooltip Text="Remove Image">
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="RemovePatientPhoto"
                                               Class="ml-1" />
                            </MudTooltip>
                        </div>

                        @if (!string.IsNullOrEmpty(uploadedFileName))
                        {
                            <span style="color:blue;" @onclick="TogglePreview">
                                @uploadedFileName
                            </span>
                        }
                        @if (showPreview && !string.IsNullOrEmpty(imagePreviewUrl))
                        {
                            <img src="@imagePreviewUrl" alt="Preview" style="max-width: 300px; max-height: 300px;" />
                        }
                    }
                </div>





                <MudTextField @bind-Value="member.UserName" Label="@Localizer["UserName"]" Required="true" />
                <MudTextField @bind-Value="member.Email" Label="@Localizer["Email"]" Required="true" Validation="ValidateEmail" />
            </div>
            <div class="custom-form-row">
                <MudTextField @bind-Value="member.FirstName" Label="@Localizer["FirstName"]" Required="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.LastName" Label="@Localizer["LastName"]" Required="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.PhoneNumber" Label="@Localizer["PhoneNumber"]" Validation="ValidatePhoneNumber" />
            </div>
            <div class="custom-form-row">
                <MudDatePicker @ref="dobDatePicker"
                               Label="@Localizer["DOB"]"
                               MaxDate="DateTime.Today"
                               Clearable="true"
                               AutoClose="true"
                               PickerVariant="PickerVariant.Dialog"
                               @bind-Date="member.DateOfBirth">
                    <PickerActions>
                        <div style="position: absolute; top: 20px; right: 20px;">
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           OnClick="@(() => CloseDatePicker(dobDatePicker))"
                                           Size="Size.Small"
                                           Style="color: white !important" />
                        </div>
                    </PickerActions>
                </MudDatePicker>
                <MudAutocomplete T="string" Required="true"
                                 Label="@Localizer["Country"]"
                                 Value="@member.Country"
                                 ValueChanged="@(value => member.Country = value)"
                                 SearchFunc="SearchCountries"
                                 Clearable="true"
                                 Dense="true"
                                 Placeholder="Search Country" />

            </div>
            <div class="custom-form-row">
                <MudTextField @bind-Value="member.PreferredName" Label="@Localizer["PreferredName"]" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.ExternalID" Label="@Localizer["ExternalID"]" />
                <MudTextField @bind-Value="member.MaritalStatus" Label="@Localizer["MaritalStatus"]" Validation="ValidateAlphabetic" />
            </div>
            <div class="custom-form-row">
                <MudTextField @bind-Value="member.SexualOrientation" Label="@Localizer["SexualOrientation"]" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.PreviousNames" Label="@Localizer["PreviousNames"]" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.SSN" Label="@Localizer["SSN"]" Validation="ValidateSSN" />
            </div>
        </MudPaper>
    }

    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleGuardianForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["GuardianDetails"]
        </MudButton>
    </div>
    @if (showGuardianForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">
            <MudGrid>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianName" Label="@Localizer["GuardianName"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianRelationship" Label="@Localizer["GuardianRelationship"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianSex" Label="@Localizer["GuardianSex"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianCity" Label="@Localizer["GuardianCity"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianState" Label="@Localizer["GuardianState"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudAutocomplete T="string"
                                     Label="@Localizer["GuardianCountry"]"
                                     Value="@guardian.GuardianCountry"
                                     ValueChanged="@(value => guardian.GuardianCountry = value)"
                                     SearchFunc="SearchCountries"
                                     Clearable="true"
                                     Dense="true"
                                     Placeholder="Search Country" />

                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianPhone" Label="@Localizer["GuardianPhone"]" FullWidth="true" Validation="ValidatePhoneNumber" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianEmail" Label="@Localizer["GuardianEmail"]" FullWidth="true" Validation="ValidateEmail" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianAddress" Label="@Localizer["GuardianAddress"]" FullWidth="true" Validation="ValidateAddress" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="guardian.GuardianSSIN" Label="@Localizer["GuardianSSIN"]" FullWidth="true" Validation="ValidateSSN" />
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleEmployerForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["EmployerDetails"]
        </MudButton>
    </div>

    @if (showEmployerForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">
            <MudGrid>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.Occupation" Label="@Localizer["Occupation"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerName" Label="@Localizer["EmployerName"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerAddress" Label="@Localizer["EmployerAddress"]" FullWidth="true" Validation="ValidateAddress" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerAddressLine2" Label="@Localizer["EmployerAddressLine2"]" FullWidth="true" Validation="ValidateAddress" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerCity" Label="@Localizer["EmployerCity"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerState" Label="@Localizer["EmployerState"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerPostalCode" Label="@Localizer["PostalCode"]" FullWidth="true" Validation="ValidatePostalCode" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudAutocomplete T="string"
                                     Label="@Localizer["EmployerCountry"]"
                                     Value="@employer.EmployerCountry"
                                     ValueChanged="@(value => employer.EmployerCountry = value)"
                                     SearchFunc="SearchCountries"
                                     Clearable="true"
                                     Dense="true"
                                     Placeholder="Search Country" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="employer.EmployerIndustry" Label="@Localizer["Industry"]" FullWidth="true" Validation="ValidateAlphabetic" />
                </MudItem>
            </MudGrid>
        </MudPaper>
    }

    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleStatsForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["StatsDetails"]
        </MudButton>
    </div>

    @if (showStatsForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">
            <div class="flex-container">
                <MudTextField @bind-Value="member.Language" Label="@Localizer["Language"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.Ethnicity" Label="@Localizer["Ethnicity"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.Race" Label="@Localizer["Race"]" FullWidth="true" Validation="ValidateAlphabetic" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="member.Nationality" Label="@Localizer["Nationality"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.FamilySize" Label="@Localizer["FamilySize"]" FullWidth="true" Validation="ValidateNumeric" />
                <MudDatePicker @ref="financialReviewDatePicker"
                               Label="@Localizer["FinancialReviewDate"]"
                               Class="custom-datepicker"
                               MaxDate="DateTime.Today"
                               Clearable="true"
                               PickerVariant="PickerVariant.Dialog"
                               @bind-Date="member.FinancialReviewDate"
                               AutoClose="true">
                    <PickerActions>
                        <div style="position: absolute; top: 20px; right: 20px;">
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           OnClick="@(() => CloseDatePicker(financialReviewDatePicker))"
                                           Size="Size.Small"
                                           Style="color: white !important" />
                        </div>
                    </PickerActions>
                </MudDatePicker>

            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="member.MonthlyIncome" Label="@Localizer["MonthlyIncome"]" FullWidth="true" Validation="ValidateDecimal" />
                <MudTextField @bind-Value="member.ReferralSource" Label="@Localizer["ReferralSource"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="member.Religion" Label="@Localizer["Religion"]" FullWidth="true" Validation="ValidateAlphabetic" />
            </div>
        </MudPaper>
    }


    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleInsuranceForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["InsuranceDetails"]
        </MudButton>
    </div>

    @if (showInsuranceForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.PrimaryInsuranceProvider" Label="@Localizer["PrimaryInsuranceProvider"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="insurance.PlanName" Label="@Localizer["PlanName"]" FullWidth="true" Validation="ValidateAlphanumeric" />
                <MudTextField @bind-Value="insurance.Subscriber" Label="@Localizer["Subscriber"]" FullWidth="true" Validation="ValidateAlphabetic" />
            </div>
            <div class="flex-container">

                <MudDatePicker @ref="effectiveDatePicker"
                               Label="@Localizer["EffectiveDate"]"
                               Class="custom-datepicker"
                               MaxDate="DateTime.Today"
                               Clearable="true"
                               PickerVariant="PickerVariant.Dialog"
                               @bind-Date="insurance.EffectiveDate"
                               AutoClose="true">
                    <PickerActions>
                        <div style="position: absolute; top: 20px; right: 20px;">
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           OnClick="@(() => CloseDatePicker(effectiveDatePicker))"
                                           Size="Size.Small"
                                           Style="color: white !important" />
                        </div>
                    </PickerActions>
                </MudDatePicker>

                <MudTextField @bind-Value="insurance.Relationship" Label="@Localizer["Relationship"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="insurance.PolicyNumber" Label="@Localizer["PolicyNumber"]" FullWidth="true" Validation="ValidateAlphanumeric" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.GroupNumber" Label="@Localizer["GroupNumber"]" FullWidth="true" Validation="ValidateAlphanumeric" />
                <MudTextField @bind-Value="insurance.AcceptAssignment" Label="@Localizer["AcceptAssignment"]" FullWidth="true" />
                <MudTextField @bind-Value="insurance.CoPay" Label="@Localizer["CoPay"]" FullWidth="true" Validation="ValidateDecimal" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.SubscriberEmployer" Label="@Localizer["SubscriberEmployer"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="insurance.Sex" Label="@Localizer["Sex"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="insurance.SubscriberAddressLine1" Label="@Localizer["SubscriberAddressLine1"]" FullWidth="true" Validation="ValidateAddress" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.SubscriberAddressLine2" Label="@Localizer["SubscriberAddressLine2"]" FullWidth="true" Validation="ValidateAddress" />
                <MudTextField @bind-Value="insurance.SubscriberCity" Label="@Localizer["SubscriberCity"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="insurance.SubscriberState" Label="@Localizer["SubscriberState"]" FullWidth="true" Validation="ValidateAlphabetic" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.SubscriberZipCode" Label="@Localizer["SubscriberZipCode"]" FullWidth="true" Validation="ValidatePostalCode" />

                <MudAutocomplete T="string"
                                 Label="@Localizer["SubscriberCountry"]"
                                 Value="@insurance.SubscriberCountry"
                                 ValueChanged="@(value => insurance.SubscriberCountry = value)"
                                 SearchFunc="SearchCountries"
                                 Clearable="true"
                                 Dense="true"
                                 Placeholder="Search Country" />
                <MudTextField @bind-Value="insurance.SubscriberPhone" Label="@Localizer["SubscriberPhone"]" FullWidth="true" Validation="ValidatePhoneNumber" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="insurance.AcceptAssignment" Label="@Localizer["AcceptAssignment"]" FullWidth="true" />
            </div>
        </MudPaper>
    }


    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleAddressForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["AddressDetails"]
        </MudButton>
    </div>

    @if (showAddressForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">
            <div class="flex-container">
                <MudTextField @bind-Value="address.AddressLine1" Label="@Localizer["AddressLine1"]" FullWidth="true" Validation="ValidateAddress" />
                <MudTextField @bind-Value="address.AddressLine2" Label="@Localizer["AddressLine2"]" FullWidth="true" Validation="ValidateAddress" />

                <MudAutocomplete T="string"
                                 Label="@Localizer["Country"]"
                                 Value="@address.Country"
                                 ValueChanged="@(value => address.Country = value)"
                                 SearchFunc="SearchCountries"
                                 Clearable="true"
                                 Dense="true"
                                 Placeholder="Search Country" />
            </div>
            <div class="flex-container">
                <MudTextField @bind-Value="address.City" Label="@Localizer["City"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="address.State" Label="@Localizer["State"]" FullWidth="true" Validation="ValidateAlphabetic" />
                <MudTextField @bind-Value="address.PostalCode" Label="@Localizer["PostalCode"]" FullWidth="true" Validation="ValidatePostalCode" />
            </div>
        </MudPaper>
    }


    <div class="button-container">
        <MudButton Variant="Variant.Filled" OnClick="ToggleMiscellaneousForm" Color="Color.Primary" Class="toggle-button">
            @Localizer["MiscellaneousDetails"]
        </MudButton>
    </div>


    @if (showMiscellaneousForm)
    {
        <MudPaper Elevation="1" Class="p-4 mb-4 full-width-paper">

            <MudDatePicker @ref="deceasedDatePicker"
                           Label="@Localizer["DeceasedDate"]"
                           Class="custom-datepicker"
                           MaxDate="DateTime.Today"
                           Clearable="true"
                           PickerVariant="PickerVariant.Dialog"
                           @bind-Date="member.DateDeceased"
                           AutoClose="true">
                <PickerActions>
                    <div style="position: absolute; top: 20px; right: 20px;">
                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                       OnClick="@(() => CloseDatePicker(deceasedDatePicker))"
                                       Size="Size.Small"
                                       Style="color: white !important" />
                    </div>
                </PickerActions>
            </MudDatePicker>
            <MudTextField @bind-Value="member.ReasonDeceased" Label="@Localizer["DeceasedReason"]" FullWidth="true" />

        </MudPaper>
    }
    <div class="button-container add-member-container">
        @if (isAddingNewMember)
        {
            <MudButton Variant="Variant.Filled"
                       OnClick="HandleSubmit"
                       Color="Color.Primary"
                       Class="submit-button themed-button mud-float-end">
                @Localizer["AddMember"]
            </MudButton>
        }
        else
        {
            <MudButton Variant="Variant.Filled"
                       OnClick="HandleDelete"
                       Color="Color.Primary"
                       Class="submit-button themed-button mud-float-end button-spacing">
                @Localizer["Delete"]
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       OnClick="HandleSubmit"
                       Color="Color.Primary"
                       Class="submit-button themed-button mud-float-end">
                @Localizer["Update"]
            </MudButton>
        }
    </div>
</MudContainer>



<style>

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 8px 16px;
        background-color: var(--mud-palette-surface);
    }

    .close-icon {
        margin-left: auto;
    }

    /* Adjust popup width if needed */
    .custom-datepicker .mud-picker {
        min-width: 300px;
    }



    .button-spacing {
        margin-right: 20px;
    }

    .mud-container-center {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 90%;
        margin: auto;
        padding-top: 75px;
    }

    .button-container {
        width: 100%;
        margin-top: 1rem;
        display: flex;
        justify-content: center;
    }

    .custom-datepicker .mud-input {
        width: 230px;
    }

    .toggle-button {
        width: 100%;
        max-width: 100%;
    }

    .full-width-paper {
        width: 100%;
    }

    .add-member-container {
        margin-top: 25px;
        padding-top: 25px;
        display: flex;
        justify-content: flex-end;
        width: 100%;
        margin-top: 1rem;
    }

    .mud-input-label {
        color: #333 !important;
    }

    .add-member-button {
        width: auto;
        margin: 0;
    }

    .mud-textfield {
        margin-bottom: 1rem;
    }

    .button-spacing {
        margin-right: 20px;
    }

    .flex-container {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
    }

        .flex-container > * {
            flex: 1;
            max-width: 33%;
            box-sizing: border-box;
        }

    .custom-form-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .custom-form-row {
        display: flex;
        gap: 1rem;
    }

        .custom-form-row > * {
            flex: 1;
            max-width: 33%;
        }

    /* Status indicators container */
    /* .status-indicators {
            display: flex;
            align-items: center;
            gap: 8px; /* Space between tick and cross */ */
    /* } */
    /* Styling for cross button */
    .cross-button {
        padding: 0 !important;
        min-width: unset !important;
        width: 18px !important;
        height: 18px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Make sure the icon inside is centered and sized appropriately */
    .cross-button ::deep .mud-icon-root {
        font-size: 14px !important;
        line-height: 1 !important;
    }

    /* Hover effect for cross button */
    .cross-button:hover {
        background-color: rgba(211, 47, 47, 0.15) !important;
    }

    .success-button {
        color: white !important;
        font-weight: 500;
    }

    .upload-button {
        min-width: 180px;
    }

    .themed-button {
        margin-top: 16px;
        margin-bottom: 16px;
        padding-left: 24px;
        padding-right: 24px;
        font-weight: 500;
        border-radius: 8px;

    }
</style>
