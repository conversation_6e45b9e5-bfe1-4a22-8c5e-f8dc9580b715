﻿@page "/PlanLabs"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer  
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System
@using Syncfusion.Blazor.DropDowns

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="Symbol">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                   OnClick="OpenNewDialogBox"
                                   Size="Size.Small" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_planLabs" Style="width: 80vw; max-width: 1100px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Plan Labs"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 4px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
               
                <SfGrid @ref="PlanLabsGrid" TValue="LabTests" GridLines="GridLine.Both" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@planlabs" AllowPaging="true" PageSettings-PageSize="5" Toolbar="@(new List<string>() { "Add" })">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionBegin="ActionBeginHandler" OnActionComplete="ActionCompletedHandler" TValue="LabTests"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="LabTestsId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="LabTest1" HeaderText="@Localizer["Primary Test"]" Width="40" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="TestOrganization" HeaderText="@Localizer["Organization"]" Width="40" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"
                        EditTemplate="@OrganizationEditTemplate" ></GridColumn>
                        <GridColumn Field="@nameof(LabTests.AssessmentData)"
                                    HeaderText="@Localizer["RelatedAssessment"]"
                                    TextAlign="TextAlign.Center"
                                    Width="100"
                                    EditTemplate="@AssessmentEditTemplate">
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" Width="15" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>


