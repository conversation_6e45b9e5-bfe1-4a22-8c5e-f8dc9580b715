﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class ProcedureGrid : ComponentBase
    {
        [Parameter] public Guid SelectedAssessmentId { get; set; }
        [Inject] ISnackbar Snackbar { get; set; }
        [Inject] SharedNotesService SharedNotesService { get; set; }
        [Inject] public ICPTService _CPTService { get; set; }
        [Inject] private ILogger<Procedures> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] public IAssessmentsService assessmentsService { get; set; }
        private SfGrid<Procedures> ProcedureGrids;
        private CPT selectedCPT;
        private List<Procedures> proceduresForAssessments = new List<Procedures>();
        private List<Procedures> procedure = new();
        private List<Procedures> addedProcedure = new();
        private List<Procedures> updatedProcedure = new();
        private List<Procedures> deletedProcedure = new();
        private List<CPT> _cptCodes { get; set; } = new List<CPT>();
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        public List<AssessmentsData> Localdata { get; set; } = new List<AssessmentsData>();
        private List<string> AssessmentDiagnosis = new List<string>();
        private List<Procedures> procedureRelatedToAssessments { get; set; } = new List<Procedures>();
        [Parameter] public EventCallback OnChange { get; set; }
        private void NotifyParentOfChanges()
        {
            OnChange.InvokeAsync();
        }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientService.PatientData.Id;
                OrgID = PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                await LoadProcedureAsync();
                Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
                proceduresForAssessments = await ProcedureService.GetProcedureByPatientId(PatientId, OrgID, Subscription);
                AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();

                procedureRelatedToAssessments = proceduresForAssessments
            .Where(proce => proce.AssessmentId == SelectedAssessmentId)
            .ToList();
                _cptCodes = await _CPTService.GetAllCPTCodesAsync();
                SharedNotesService.OnChange += UpdateAssessments;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }
        private async Task LoadProcedureAsync()
        {
            try
            {
                procedure = await ProcedureService.LoadProcedureAsync(PatientId, OrgID, Subscription);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading Procedure");
            }
        }
        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();
        }
        private async Task<IEnumerable<CPT>> SearchCPTCodes(string value, CancellationToken cancellationToken)
        {
            return _cptCodes.Where(cpt =>
                (cpt.CPTCode?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (cpt.Description?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", process.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        process.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(process.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");

            builder.CloseComponent();
        };
        private async void AddNewProcedure()
        {
            if (selectedCPT == null)
            {
                Snackbar.Add(Localizer["Please select a CPT code first"], Severity.Warning);
                return;
            }

            var newProcedure = new Procedures
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = Guid.Parse(User.id),
                CPTCode = selectedCPT.CPTCode,
                OrderedBy = User.givenName + " " + User.surname,
                Description = selectedCPT.Description,
                Notes = string.Empty,
                OrderDate = DateTime.Now,
                CreatedByUserId = Guid.Parse(User.id),
                UpdatedByUserId = Guid.Parse(User.id),
                LastUpdatedDate = DateTime.Now,
                AssessmentId = SelectedAssessmentId, 
                AssessmentData = Localdata.FirstOrDefault(a => a.AssessmentsID == SelectedAssessmentId)?.Diagnosis
            };

            procedure.Add(newProcedure);
            procedureRelatedToAssessments.Add(newProcedure);
            addedProcedure.Add(newProcedure);
            selectedCPT = null;
            await ProcedureGrids.Refresh();
            StateHasChanged();
            selectedCPT = null;
        }

        public void ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<Procedures> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedProcedure.Add(args.Data);
                args.Data.IsDeleted = false;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedProcedure.Contains(args.Data) && !updatedProcedure.Contains(args.Data))
                {
                    args.Data.UpdatedByUserId = Guid.Parse(User.id);
                    args.Data.LastUpdatedDate = DateTime.Now;
                    updatedProcedure.Add(args.Data);
                }
            }
        }

        private async Task SaveChanges()
        {
            try
            {

                if (addedProcedure.Any())
                {
                    if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                    {
                        Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                        return;
                    }

                    await ProcedureService.AddProcedureAsync(addedProcedure, OrgID, Subscription);
                    addedProcedure.Clear();
                }

                if (updatedProcedure.Any())
                {
                    if (updatedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                    {
                        Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                        return;
                    }
                    await ProcedureService.UpdateProcedureListAsync(updatedProcedure, OrgID, Subscription);
                    updatedProcedure.Clear();
                }

                if (deletedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(deletedProcedure, OrgID, Subscription);
                    deletedProcedure.Clear();
                }
                StateHasChanged();
                OnInitializedAsync();
                NotifyParentOfChanges();
                await LoadProcedureAsync();
                Snackbar.Add(Localizer["Changes saved successfully!"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        private async Task CancelChanges()
        {
            addedProcedure.Clear();
            updatedProcedure.Clear();
            deletedProcedure.Clear();
            await LoadProcedureAsync();
            NotifyParentOfChanges();
            OnInitializedAsync();
            await ProcedureGrids.Refresh();
        }

        private async Task OnCPTSelected(CPT selected)
        {
            selectedCPT = selected;
            StateHasChanged();
        }
    }
}
