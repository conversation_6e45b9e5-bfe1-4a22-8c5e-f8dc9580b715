﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
using System.Linq;
using TeyaWebApp.TeyaAIScribeResource;
using System.Text.RegularExpressions;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.Graph.Models.Partners.Billing;
using Syncfusion.Blazor;

namespace TeyaWebApp.Components.Pages
{
    public partial class Allergies : ComponentBase
    {
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public IAllergyService AllergyService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        [Inject] private ILogger<Allergies> _logger { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private string drugName;
        private string editorContent;
        private MudDialog _allergicdialog;
        private SfRichTextEditor RichTextEditor;
        public SfGrid<Allergy> AllergyGrid { get; set; }
        private List<Allergy> _allergiesData { get; set; }
        private List<Allergy> _localAllergiesData { get; set; }
        private List<Allergy> _originalAllergiesData { get; set; }
        private List<Allergy> _deleteList { get; set; } = new List<Allergy>();
        protected Dictionary<string, string> DrugNames { get; set; } = new Dictionary<string, string>();
        protected Dictionary<string, string> FDBAllergiesList { get; set; } = new Dictionary<string, string>();
      
        private Guid? OrganizationID { get; set; }
        public enum Source { RxNorm,FDB }
        private string selectedDatabase = Source.RxNorm.ToString();

        [Inject] IDialogService DialogService { get; set; }

        public Guid patientID { get; set; } 
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        public string? ManualContent { get; set; }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };

        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            patientID = PatientID;
            OrganizationID = OrgId;
            ManualContent = Data;

            Subscription = UserContext.ActiveUserSubscription;
            _originalAllergiesData = await AllergyService.GetAllergyByIdAsyncAndIsActive(patientID, OrganizationID, Subscription);
            _localAllergiesData = _originalAllergiesData.ToList();
            editorContent = GenerateRichTextContent(ManualContent);

            // Start Phase 2 in background without awaiting
            _ = LoadAllergyDataAsync();
        }

        private async Task LoadAllergyDataAsync()
        {
            var drugNamesList = await RxNormService.GetAllDrugNames();
            DrugNames = drugNamesList.ToDictionary(name => RxNormService.GetRxcuiByName(name), name => name);
        }
        /// <summary>
        /// Generates rich text editor content from allergy data.
        /// </summary>
        private string GenerateRteContent(List<Allergy> allergies)
        {
            return string.Join("<p>", allergies.Select(m => $"{m.DrugName}, {m.Classification}, {m.Agent} {m.Reaction}, {m.Type}"));
        }

        /// <summary>
        /// Update value in Drug Name List
        /// </summary>
        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            drugName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>
        private CancellationTokenSource _searchAllergiesCancellationTokenSource;

        protected async Task<IEnumerable<string>> SearchDrugNames(string searchTerm, CancellationToken cancellationToken)
        {
            if (selectedDatabase == Source.RxNorm.ToString())
            {
                // Keep existing RxNorm logic (synchronous)
                return string.IsNullOrWhiteSpace(searchTerm)
                    ? DrugNames.Values.AsEnumerable()
                    : DrugNames.Values
                        .Where(b => !string.IsNullOrEmpty(b) && b.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                return await SearchFDBAllergies(searchTerm, cancellationToken);
            }

            return Enumerable.Empty<string>();
        }

        private async Task<IEnumerable<string>> SearchFDBAllergies(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<string>();

            // Cancel previous search if still running
            _searchAllergiesCancellationTokenSource?.Cancel();
            _searchAllergiesCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchAllergiesCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var allergies = await FDBService.GetAllergiesBySearchTerm(searchTerm);
                return allergies.Select(a => a.DAM_CONCEPT_ID_DESC);
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search FDB allergies");
                return Enumerable.Empty<string>();
            }
        }


        /// <summary>
        /// Add function to add new row.
        /// </summary>
        private async void AddNewAllergy()
        {
            var newAllergy = new Allergy
            {
                MedicineId = Guid.NewGuid(),
                PatientId = patientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrganizationID,
                DrugName = string.IsNullOrEmpty(drugName) ? "Not Specified" : drugName,
                Classification = (drugName == "Not Specified" || string.IsNullOrEmpty(drugName)) ? "Unstructured" : "Structured",
                Agent = "Not Specified",
                Type = "Not Specified",
                Reaction = "Not Specified",
                IsActive = true,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = Guid.Parse(User.id),
                CreatedOn = DateTime.Now,
                UpdatedOn = DateTime.Now,
                AllergyInfo = "Not Specified",
                Substance = "Not Specified",
                DrugDetails = null,
                IsNew = true
            };

            _localAllergiesData.Add(newAllergy);
            AllergyGrid.Refresh();
            ResetInputFields();
            StateHasChanged();
        }

        private void ResetInputFields()
        {
            drugName = string.Empty;
        }

        /// <summary>
        /// Handles row deletions in the grid.
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<Allergy> args)
        {
            
        }

        /// <summary>
        /// Handles row updates in the grid.
        /// </summary>
        public  async Task ActionBeginHandler(ActionEventArgs<Allergy> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedOn = DateTime.Now;
                _deleteList.Add(args.Data);
                _localAllergiesData.Remove(args.Data);
            }
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!Regex.IsMatch(args.Data.Classification, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["ClassificationInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Agent, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["AgentInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Reaction, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["ReactionInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Type, "^[A-Za-z0-9\\-]+$"))
                {
                    Snackbar.Add(Localizer["TypeInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedOn = DateTime.Now;
            }
        }

        /// <summary>
        /// Save the changes.
        /// </summary>
        private async Task SaveChanges()
        {
            try
            {
                var activeLocalAllergies = _localAllergiesData.Where(a => !_deleteList.Contains(a)).ToList();
                var newAllergies = activeLocalAllergies.Where(a => a.IsNew).ToList();
                var updatedAllergies = activeLocalAllergies.Where(a => !a.IsNew).ToList();

                await AllergyService.UpdateAllergyListAsync(newAllergies, updatedAllergies, _deleteList, patientID, OrganizationID, Subscription);

                _originalAllergiesData = await AllergyService.GetAllergyByIdAsyncAndIsActive(patientID, OrganizationID, Subscription);

                //editorContent = GenerateRteContent(_originalAllergiesData);
                editorContent = GenerateRichTextContent(ManualContent);

                foreach (var allergy in _localAllergiesData)
                {
                    allergy.IsNew = false;
                }

                _deleteList.Clear();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);

                await HandleDynamicComponentUpdate();
                CloseAddTaskDialog();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving changes: {ex}");
                Snackbar.Add("SaveError", Severity.Error);
            }
        }

        /// <summary>
        /// Undo Changes When click on cancel.
        /// </summary>
        private async Task CancelChanges()
        {
            _localAllergiesData = _originalAllergiesData.ToList();
            _deleteList.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Opens the dialog and initializes local data.
        /// </summary>
        private async Task OpenAddTaskDialog()
        {
            _localAllergiesData = _originalAllergiesData.ToList();
            _deleteList.Clear();

            _allergicdialog.ShowAsync();
        }

        /// <summary>
        /// Closes the dialog.
        /// </summary>
        private void CloseAddTaskDialog()
        {
            ResetInputFields();
            _allergicdialog.CloseAsync();
        }



        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;
            string allergyContent = _localAllergiesData != null && _localAllergiesData.Any()
                ? string.Join(" ", _localAllergiesData
                    .Where(a => !string.IsNullOrEmpty(a.DrugName))
                    .Select(a => $"<ul><li style='margin-left: 20px;'>{a.DrugName}, {a.Classification}, {a.Agent}, {a.Reaction}, {a.Type}</li></ul>"))
                : string.Empty;
            return $@"<div>
                <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
                {manualData}
                <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
                {allergyContent}
                </div>";
        }





        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}