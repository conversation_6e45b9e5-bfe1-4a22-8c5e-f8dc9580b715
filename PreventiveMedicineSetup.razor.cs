﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Unity;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class PreventiveMedicineSetup : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject] private IPreventiveMedicineService PreventiveMedicineService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }

        private List<PMCategory> categories = new();
        private List<PMSubCategory> subCategories = new();
        private List<PMSymptoms> symptoms = new();

        private Guid _selectedCategoryId = Guid.Empty;
        private Guid _selectedSubCategoryId = Guid.Empty;
        private Guid _selectedCategoryIdForSymptom = Guid.Empty;
        private Guid _selectedSubCategoryIdForSymptom = Guid.Empty;

        private PMCategory _newCategory = new();
        private PMSubCategory _newSubCategory = new();
        private PMSymptoms _newSymptom = new();

        private List<PMSubCategory> _filteredSubCategoriesForSymptom = new();

        private enum FormType { None, Category, Subcategory, Symptom }
        private FormType activeForm = FormType.None;

        protected override async Task OnInitializedAsync()
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            categories = await PreventiveMedicineService.GetAllPMCategoriesAsync();
            subCategories = await PreventiveMedicineService.GetAllPMSubCategoriesAsync();
            symptoms = await PreventiveMedicineService.GetAllPMSymptomsAsync();
        }

        private void ShowForm(FormType formType)
        {
            activeForm = formType;

            if (formType == FormType.Subcategory)
            {
                _newSubCategory = new();
                _selectedCategoryIdForSymptom = Guid.Empty;
            }

            if (formType == FormType.Symptom)
            {
                _newSymptom = new();
                _selectedCategoryIdForSymptom = _selectedCategoryId;
                _selectedSubCategoryIdForSymptom = _selectedSubCategoryId;

                if (_selectedCategoryIdForSymptom != Guid.Empty)
                {
                    _filteredSubCategoriesForSymptom = subCategories
                        .Where(s => s.PMCategoryId == _selectedCategoryIdForSymptom)
                        .ToList();
                }
            }
        }

        private void SelectCategory(Guid categoryId)
        {
            _selectedCategoryId = categoryId;
            _selectedSubCategoryId = Guid.Empty;
            activeForm = FormType.None;
        }

        private void SelectSubCategory(Guid subCategoryId)
        {
            _selectedSubCategoryId = subCategoryId;
            activeForm = FormType.None;
        }

        private void CancelForm()
        {
            activeForm = FormType.None;
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task AddCategory()
        {
            if (string.IsNullOrWhiteSpace(_newCategory.PMCategoryName))
            {
                Snackbar.Add(Localizer["CategoryNameRequired"], Severity.Error);
                return;
            }
            if (categories.Any(c => c.PMCategoryName.Equals(_newCategory.PMCategoryName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["CategoryExists"], Severity.Error);
                return;
            }

            var categoryToAdd = new PMCategory
            {
                PMCategoryId = Guid.NewGuid(),
                PMCategoryName = _newCategory.PMCategoryName,
                PMCategoryDescription = _newCategory.PMCategoryDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMCategoryAsync(new List<PMCategory> { categoryToAdd });
                Snackbar.Add(Localizer["CategoryAdded"], Severity.Success);
                _newCategory = new();
                await LoadData();
                ShowForm(FormType.Category);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingCategory", ex.Message], Severity.Error);
            }
            StateHasChanged();
        }

        private async Task AddSubCategory()
        {
            if (string.IsNullOrWhiteSpace(_newSubCategory.PMSubcategoryName))
            {
                Snackbar.Add(Localizer["SubcategoryNameRequired"], Severity.Error);
                return;
            }

            if (_selectedCategoryId == Guid.Empty)
            {
                Snackbar.Add(Localizer["SelectACategory"], Severity.Error);
                return;
            }

            if (subCategories.Any(sc =>
               sc.PMCategoryId == _selectedCategoryId &&
               sc.PMSubcategoryName.Equals(_newSubCategory.PMSubcategoryName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["SubcategoryExists"], Severity.Error);
                return;
            }

            var subCategoryToAdd = new PMSubCategory
            {
                PMSubcategoryId = Guid.NewGuid(),
                PMCategoryId = _selectedCategoryId,
                PMSubcategoryName = _newSubCategory.PMSubcategoryName,
                PMSubcategoryDescription = _newSubCategory.PMSubcategoryDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMSubCategoryAsync(new List<PMSubCategory> { subCategoryToAdd });
                Snackbar.Add(Localizer["SubcategoryAdded"], Severity.Success);
                _newSubCategory = new();
                await LoadData();
                ShowForm(FormType.Subcategory);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingSubcategory", ex.Message], Severity.Error);
            }
            StateHasChanged();
        }

        private async Task AddSymptom()
        {
            if (string.IsNullOrWhiteSpace(_newSymptom.PMSymptomName))
            {
                Snackbar.Add(Localizer["SymptomNameRequired"], Severity.Error);
                return;
            }

            var subCategoryId = _selectedSubCategoryIdForSymptom != Guid.Empty
                ? _selectedSubCategoryIdForSymptom
                : _selectedSubCategoryId;

            if (subCategoryId == Guid.Empty)
            {
                Snackbar.Add(Localizer["SelectSubcategory"], Severity.Error);
                return;
            }

            if (symptoms.Any(s =>
               s.PMSubCategoryId == subCategoryId &&
               s.PMSymptomName.Equals(_newSymptom.PMSymptomName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["SymptomExists"], Severity.Error);
                return;
            }

            var symptomToAdd = new PMSymptoms
            {
                PMSymptomId = Guid.NewGuid(),
                PMSubCategoryId = subCategoryId,
                PMSymptomName = _newSymptom.PMSymptomName,
                PMSymptomDescription = _newSymptom.PMSymptomDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMSymptomsAsync(new List<PMSymptoms> { symptomToAdd });
                Snackbar.Add(Localizer["SymptomAdded"], Severity.Success);
                _newSymptom = new();
                await LoadData();
                activeForm = FormType.Symptom;
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingSymptom", ex.Message], Severity.Error);
            }
            StateHasChanged();
        }
    }
}
