﻿using Microsoft.AspNetCore.Components;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class TreatmentPage : ComponentBase
    {
        private enum ComponentType
        {
            None,
            Medication,
            Labs,
            Procedure
        }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IAssessmentsService _AssessmentsService { get; set; }
        [Inject] public PatientService _PatientService { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] public IProcedureService _proceduresService { get; set; }
        private ComponentType selectedComponent = ComponentType.None;
        private Guid PatientID;
        private Guid? OrgID { get; set; }
        private List<AssessmentsData> assessments { get; set; } = new();
        private Dictionary<string, List<string>> medicationRelatedToAssessments { get; set; } = new();
        private Dictionary<Guid, string> visibleAssessmentDiagnoses = new();
        private Guid selectedChiefComplaintId;
        private Guid selectedAssessmentId;
        private List<LabTests> labsRelatedToAssessments { get; set; }
        private List<Procedures> proceduresRelatedToAssessments { get; set; } = new();

        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientID, OrgID, Subscription);
            medicationRelatedToAssessments = await _AssessmentsService.GetAllMedicationsRelatedToAssessments(PatientID, OrgID, Subscription);
            labsRelatedToAssessments = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientID);
            proceduresRelatedToAssessments = await _proceduresService.GetProcedureByPatientId(PatientID, OrgID, Subscription);
            foreach (var assessment in assessments)
            {
                visibleAssessmentDiagnoses[assessment.AssessmentsID] = assessment.Diagnosis;
            }
        }

        private void ToggleMedicationVisibility(Guid chiefComplaintId)
        {
            selectedChiefComplaintId = chiefComplaintId;
            selectedComponent = selectedComponent == ComponentType.Medication ? ComponentType.None : ComponentType.Medication;
        }

        private void ToggleLabsVisibility(Guid assessmentId)
        {
            selectedAssessmentId = assessmentId;
            selectedComponent = selectedComponent == ComponentType.Labs ? ComponentType.None : ComponentType.Labs;
        }
        private void ToggleProcedureVisibility(Guid assessmentId)
        {
            selectedAssessmentId = assessmentId;
            selectedComponent = selectedComponent == ComponentType.Procedure ? ComponentType.None : ComponentType.Procedure;
        }

        private async void HandleGridChange()
        {
            await OnInitializedAsync();
            StateHasChanged();
        }
    }
}
