﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Syncfusion.Blazor.Buttons;
using Unity;
using Syncfusion.Blazor.Navigations;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using System.Text.RegularExpressions;
using System.Text.Json;
using System.Text;
using Syncfusion.Windows.Shared;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;

namespace TeyaWebApp.Components.Pages
{
    public partial class QualityMeasure
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IHttpClientFactory HttpClientFactory { get; set; }
        [Inject] public IMeasureService MeasureService { get; set; }
        [Inject] private IDialogService DialogService { get; set; } 


        private MudDialog _qualityMeasureDialog;
        private SfGrid<Measure> MeasuresGrid;
        private string SearchTerm = string.Empty;

        private string _selectedState;
        private List<string> _states = new() { "YES", "NO" }; // CDSS dropdown options
        private List<Measure> Measures = new(); // Your main grid data source
        private List<Measure> addedMeasures = new();
        private List<Measure> deletedMeasures = new();
        private List<Measure> updatedMeasures = new();
        private Measure NewMeasure = new();     // Used for the form binding



        protected override async Task OnInitializedAsync()
        {
            try
            {
                Measures = await MeasureService.GetAllMeasuresAsync();
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["Error loading measures"], Severity.Error);
                Console.WriteLine($"Error loading measures: {ex.Message}");
            }
        }


        private void CloseDialog()
        {
            _qualityMeasureDialog.CloseAsync();
        }




        private void AddMeasures()
        {
            if (!string.IsNullOrWhiteSpace(NewMeasure.MeasureTitle))
            {
                // Clone to prevent binding issues
                var measureToAdd = new Measure
                {
                    Id = Guid.NewGuid(),
                    MeasureTitle = NewMeasure.MeasureTitle,
                    MeasureNumerator = NewMeasure.MeasureNumerator,
                    MeasureDenominator = NewMeasure.MeasureDenominator,
                    MeasureDescription = NewMeasure.MeasureDescription,
                    CDSS = NewMeasure.CDSS,
                    MeasureType = NewMeasure.MeasureType,
                    OrderSetLinked = NewMeasure.OrderSetLinked,
                    IsDeleted = true,

                };

                Measures.Add(measureToAdd); // Add to list
                addedMeasures.Add(measureToAdd);
                MeasuresGrid.Refresh(); // Refresh grid
                Snackbar.Add("Measure added successfully!", Severity.Success);
                               
                NewMeasure = new Measure();
            }
            else
            {
                Snackbar.Add("Measure title is required.", Severity.Warning);
            }
        }

        public async Task ActionBeginHandlerAsync(ActionEventArgs<Measure> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Delete"],
                    Localizer["Do you want to delete this entry?"],
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }

                // Only mark for deletion if user confirms  
                args.Data.IsDeleted = true; // Corrected from `false` to `true`  
                deletedMeasures.Add(args.Data);
                Snackbar.Add(Localizer["Entry deleted successfully."], Severity.Warning);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedMeasures.Contains(args.Data) && !updatedMeasures.Contains(args.Data))
                {
                    updatedMeasures.Add(args.Data);
                }
            }
        }

        private async Task CancelChanges()
        {
            addedMeasures.Clear();
            updatedMeasures.Clear();
            deletedMeasures.Clear();
            CloseDialog();
            Snackbar.Add("Changes discarded", Severity.Info);
        }

        private async Task SaveChanges()
        {
            try
            {
                
                if (addedMeasures.Any())
                {
                    await MeasureService.AddMeasuresAsync(addedMeasures);
                    addedMeasures.Clear();
                }

                // Handle updates
                if (updatedMeasures.Any())
                {
                    await MeasureService.UpdateMeasuresListAsync(updatedMeasures);
                    updatedMeasures.Clear();
                }

                // Handle deletes
                if (deletedMeasures.Any())
                {
                    await MeasureService.UpdateMeasuresListAsync(deletedMeasures);
                    deletedMeasures.Clear();
                }                
                
                CloseDialog();
                Snackbar.Add(Localizer["Changes saved successfully!"], Severity.Success);
            }
            catch (Exception ex)
            {
                
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        

        private void HandleBackdropClick()
        {
            Snackbar.Add("Use the Cancel button to close the dialog", Severity.Info);
        }

        private async Task OpenNewDialogBox()
        {
            await _qualityMeasureDialog.ShowAsync();
        }

        /// <summary>
        /// Calls your Azure OpenAI GPT deployment to suggest up to 3 measures
        /// from the free‑text SearchTerm, parses them, and reloads the grid.
        /// </summary>
        private async Task SuggestMeasures()
        {
            
            // Build messages
            var systemMsg = "You are a helpful assistant that suggests clinical quality measures.";
            var userMsg =
               "Given this patient summary:\n" +
               $"  \"{SearchTerm}\"\n\n" +
               "Suggest up to 3 appropriate clinical quality measures.\n" +
               "For each measure, output exactly in this form:\n\n" +
               "  MeasureTitle = <short title>\n" +
               "  MeasureNumerator = <1–2 line description>\n" +
               "  MeasureDenominator = <1–2 line description>\n" +
               "  MeasureDescription = <2–3 line description>\n" +
               "  CDSS = <Yes or No>\n" +
               "  MeasureType = <Clinical / Operational / Quality>\n" +
               "  OrderSetLinked = <OrderSet name or ID>\n\n" +
               "Separate measures by a blank line.";


            var reply = await MeasureService.AskGptAsync(systemMsg, userMsg);

            if (string.IsNullOrWhiteSpace(reply))
            {
                Snackbar.Add("No suggestions returned.", Severity.Warning);
                return;
            }

            // Now this is valid, since `reply` is a string:
            var blocks = Regex
                .Split(reply.Trim(), @"\r?\n\s*\r?\n")
                .Where(b => !string.IsNullOrWhiteSpace(b))
                .Select(ParseMeasureResponse)
                .ToList();



            if (!blocks.Any())
            {
                Snackbar.Add("Could not parse any measures.", Severity.Error);
                return;
            }

            foreach (var measure in blocks)
            {
                if (string.IsNullOrWhiteSpace(measure.MeasureTitle))
                {
                    continue; // Skip blank or malformed measure
                }

                // (optional) skip duplicates if you’d like:
                if (!Measures.Any(m => m.MeasureTitle == measure.MeasureTitle))
                {
                    measure.Id = Guid.NewGuid();       
                    measure.IsDeleted = true;         
                    Measures.Add(measure);
                    addedMeasures.Add(measure);
                }
                   
            }


            await MeasuresGrid.Refresh(); // rebind the grid
            SearchTerm = string.Empty; // clear the search term
        }

        /// <summary>
        /// Parses a single GPT‑output block into your Measure model.
        /// </summary>
        private Measure ParseMeasureResponse(string block)
        {
            var measure = new Measure();

            // split on any single line break
            var lines = block
                .Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var raw in lines)
            {
                var line = raw.Trim();
                // split into two parts at the first '='
                var parts = line.Split('=', 2, StringSplitOptions.None);
                if (parts.Length != 2)
                    continue;

                var key = parts[0].Trim();
                var val = parts[1].Trim();

                switch (key)
                {
                    case "MeasureTitle":
                        measure.MeasureTitle = val;
                        break;
                    case "MeasureNumerator":
                        measure.MeasureNumerator = val;
                        break;
                    case "MeasureDenominator":
                        measure.MeasureDenominator = val;
                        break;
                    case "MeasureDescription":
                        measure.MeasureDescription = val;
                        break;
                    case "CDSS":
                        measure.CDSS = val;
                        break;
                    case "MeasureType":
                        measure.MeasureType = val;
                        break;
                    case "OrderSetLinked":
                        measure.OrderSetLinked = val;
                        break;
                }
            }

            return measure;
        }
    }
}