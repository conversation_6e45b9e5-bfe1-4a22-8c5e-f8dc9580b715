﻿@page "/templates"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIViewModels.ViewModels
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "templatesAccessPolicy")]
@using MudBlazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Components.Layout
@using Syncfusion.Blazor.Grids
@using System.Text.Json
@using TeyaWebApp.TeyaAIScribeResources
@inject ITemplateService TemplateService
@inject IVisitTypeService VisitTypeService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ILogger<Templates> Logger
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IMemberService MemberService
@using Syncfusion.Blazor.Navigations
@using System.Collections.Generic
@layout Admin


<div style="padding: 24px;">
    <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; margin-bottom: 8px; font-weight: 600;">
        <strong>@Localizer["Visit Type"]</strong>
    </p>

    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px; padding: 12px 0;">
        <SfDropDownList TValue="string" TItem="string"
                        Placeholder="Select VisitType"
                        DataSource="@data"
                        @bind-Value="@DropDownValue"
                        Width="300px">
            <DropDownListEvents TValue="string" TItem="string" OnValueSelect="@OnValueSelecthandler"></DropDownListEvents>
        </SfDropDownList>

        <SfButton OnClick="ResetSelection"
                  style="padding: 8px 16px; margin-left: 8px;">
            Reset
        </SfButton>
    </div>
</div>



<MudPaper Class="pa-4" Elevation="0">
    <MudText Typo="Typo.h6" Class="mb-4 ml-6">Available Templates</MudText>

    <div style="max-width: 1200px;">


        <GenericGrid TValue="TemplateData" DataSource="@ProviderData" GridLines="GridLine.Both">
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
            <GridPageSettings PageSize="10"></GridPageSettings>
            <GridColumns>
                <GridColumn Field="@nameof(TemplateData.IsDefault)" HeaderText="@Localizer["Default"]" Width="150" TextAlign="TextAlign.Center">
                    <Template Context="data">
                        @{
                            var templateData = data as TemplateData;
                        }
                        <SfCheckBox Checked="@templateData?.IsDefault" @onchange="@(args => OnIsDefaultChange(args, templateData))">
                        </SfCheckBox>
                    </Template>
                </GridColumn>
                <GridColumn Field="@nameof(TemplateData.VisitType)" HeaderText="@Localizer["Visit Type"]" Width="200" TextAlign="TextAlign.Center">
                    <Template Context="data">
                        @{
                            var templateData = data as TemplateData;
                        }
                        <span>@(string.IsNullOrEmpty(templateData?.VisitType) ? @Localizer["Not Assigned"] : templateData.VisitType)</span>
                    </Template>
                </GridColumn>
                <GridColumn Field="@nameof(TemplateData.TemplateName)" HeaderText="@Localizer["Template Name"]" Width="200" TextAlign="TextAlign.Center">
                    <Template Context="data">
                        @{
                            var templateData = data as TemplateData;
                        }
                        <span style="cursor: pointer;" OnClick="() => EditTemplate(templateData)">
                            @templateData.TemplateName
                        </span>
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Sections" Width="500" TextAlign="TextAlign.Center">
                    <Template Context="data">
                        @{
                            var templateData = data as TemplateData;
                            var templateDictionary = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, TemplateField>>>(templateData.Template);
                            List<string> Keys = new List<string>();
                            if (templateDictionary != null)
                            {
                                Keys = templateDictionary.Values
                                .Where(innerDict => innerDict != null)
                                .SelectMany(innerDict => innerDict.Keys)
                                .ToList();
                            }
                            string allKeys = string.Join(", ", Keys);

                            int maxLength = 50;
                            string displayText = allKeys.Length > maxLength ? allKeys.Substring(0, maxLength) + "..." : allKeys;
                        }

                        <span style="cursor: pointer;" @onclick="() => EditTemplate(templateData)">
                            @displayText
                        </span>
                    </Template>
                </GridColumn>

                <GridColumn HeaderText="Actions" Width="140" TextAlign="TextAlign.Center">
                    <Template Context="data">
                        @{
                            var templateData = data as TemplateData;
                        }
                        <!-- Edit button -->
                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                       Color="Color.Primary"
                                       OnClick="@(() => EditTemplate(templateData))"
                                       Class="ml-2">
                        </MudIconButton>
                        <!-- Delete button -->
                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                       Color="Color.Error"
                                       OnClick="@(() => DeleteTemplate(templateData))"
                                       Class="ml-2">
                        </MudIconButton>
                    </Template>
                </GridColumn>
            </GridColumns>
        </GenericGrid>
    </div>
    <MudGrid>

        <MudItem xs="12" Class="mt-6 ml-6">
            <GenericButton Variant="Variant.Filled" OnClick="@ToggleCardVisibility" ButtonStyle="margin-top: 20px; margin-bottom: 20px; display: block; width: 200px;">
                @Localizer["+ New Template"]
            </GenericButton>
        </MudItem>
        @if (isAddSectionVisible)
        {
            <MudItem xs="10" Class="d-flex ml-6">
                <MudItem xs="4" Class="mt-6 ml-6">
                    <MudText Typo="Typo.h6" Class="mb-4">Sections</MudText>
                    <MudList T="TemplateSection" Class="medical-sections">
                        @foreach (var sectionDisplay in TemplateSections)
                        {
                            <MudListItem Class="custom-disabled-listheader-item" Disabled="true"><b>@sectionDisplay.SectionName</b></MudListItem>
                            @foreach (var field in sectionDisplay.Fields)
                            {
                                <MudListItem Class="custom-disabled-list-item" Disabled="true" Value="field" Style="color:black;">@field.FieldName</MudListItem>
                            }
                        }
                    </MudList>
                </MudItem>
                <MudItem xs="7" Class="mt-6 ml-6">
                    <MudText Typo="Typo.h6" Class="mb-4">Section Settings</MudText>
                    <MudCard Class="mb-4" Style="max-width: 600px;">

                        <MudCardContent Style="padding: 10px;">
                            <MudGrid>
                                <MudItem xs="6">
                                    <MudTextField Label="@Localizer["Template Name"]" @bind-Value="@templateName" Required="true"></MudTextField>
                                </MudItem>

                                <MudItem xs="6" Class="d-flex align-items-center justify-content-end">
                                    <MudButton Size="Size.Small" OnClick="@AddSectionHeader" Color="Color.Primary" Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-2" />@Localizer["Add Header"]
                                    </MudButton>
                                </MudItem>
                            </MudGrid>

                            <MudGrid Class="mt-2">
                                <MudItem xs="6">
                                    <MudText Typo="Typo.subtitle1">Select Visit Type</MudText>
                                </MudItem>
                                <MudItem xs="6"> 
                                    <SfDropDownList TItem="string" TValue="string"
                                                    Placeholder="Select Visit Type"
                                                    DataSource="@VisitTypes.Select(v => v.VisitName).ToList()"
                                                    Value="@SelectedVisitType"
                                                    ValueChanged="OnVisitTypeChange">
                                    </SfDropDownList>
                                </MudItem>
                            </MudGrid>
                            @for (int j = 0; j < TemplateSections.Count; j++)
                            {
                                var sectionIndex = j;
                                <MudGrid Class="mt-2">
                                    <MudItem xs="6">
                                        <MudButton OnClick="@(() => ToggleSection(sectionIndex))" Color="Color.Primary" Variant="Variant.Text">
                                            <MudIcon Icon="@(TemplateSections[sectionIndex].IsExpanded ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.ExpandMore)" Class="mr-2" />
                                            <MudText Style="font-weight:bold">@TemplateSections[sectionIndex].SectionName</MudText>
                                        </MudButton>
                                    </MudItem>
                                    <MudItem xs="6" Class="d-flex align-items-center justify-content-end">
                                        <MudButton Size="Size.Small" Color="Color.Primary" OnClick="@(() => AddTextBox(sectionIndex))" Typo="Typo.body2">
                                            <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-2" />@Localizer["Add Section"]
                                        </MudButton>
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                       Color="Color.Error"
                                                       OnClick="@(() => DeleteSection(sectionIndex))"
                                                       Class="ml-2" />
                                    </MudItem>
                                </MudGrid>

                                @if (TemplateSections[sectionIndex].IsExpanded)
                                {
                                    <MudGrid>
                                        <MudItem xs="12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <MudText Typo="Typo.body2">Header Title</MudText>
                                                <MudItem xs="6" Class="ml-auto pa-0">
                                                    <MudTextField @bind-Value="@TemplateSections[sectionIndex].SectionName"
                                                                  Variant="Variant.Outlined"
                                                                  Margin="Margin.Dense" />
                                                </MudItem>
                                            </div>
                                        </MudItem>
                                    </MudGrid>
                                    @for (int i = 0; i < TemplateSections[sectionIndex].Fields.Count; i++)
                                    {
                                        var fieldIndex = i;
                                        <MudCard Class="mt-3" Style="background-color: rgb(249, 250, 252);">
                                            <MudCardHeader Class="py-1">
                                                <MudItem xs="12">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <MudText Typo="Typo.body1">@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName</MudText>
                                                        <MudItem Class="ml-auto pa-0">
                                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                           Color="Color.Error"
                                                                           OnClick="@(() => RemoveTextBox(fieldIndex, sectionIndex))"
                                                                           Class="ml-2" />
                                                        </MudItem>
                                                    </div>
                                                </MudItem>
                                            </MudCardHeader>
                                            <MudDivider DividerType="DividerType.Middle" Style="background-color: gray;" />
                                            <MudCardContent>
                                                <MudGrid>
                                                    <MudItem xs="12">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <MudText Typo="Typo.body2">Section Title</MudText>
                                                            <MudItem xs="6" Class="ml-auto pa-0">
                                                                <MudTextField @bind-Value="@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName"
                                                                              Variant="Variant.Outlined"
                                                                              Margin="Margin.Dense" />
                                                            </MudItem>
                                                        </div>
                                                    </MudItem>
                                                </MudGrid>

                                                <div class="d-flex align-items-center mt-2">
                                                    <MudText Class="mb-1" Typo="Typo.body2">Section style</MudText>
                                                    <div class="ml-auto">
                                                        <MudButtonGroup Color="Color.Primary" OverrideStyles="_overrideStyles">
                                                            @foreach (var style in new[] { "Auto", "Bullet", "Paragraph" })
                                                            {
                                                                var currentStyle = style;
                                                                <MudButton Size="Size.Small"
                                                                           Color="Color.Primary"
                                                                           Variant="@GetButtonVariant(currentStyle, fieldIndex, sectionIndex)"
                                                                           OnClick="@(() => SetSectionStyle(currentStyle, fieldIndex, sectionIndex))"
                                                                           Typo="Typo.body2">@Localizer[currentStyle]</MudButton>
                                                            }
                                                        </MudButtonGroup>
                                                    </div>
                                                </div>

                                                <MudItem xs="12" Class="mt-1">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <MudText Typo="Typo.body2">Custom Instructions</MudText>
                                                        <MudItem xs="8" Class="ml-auto pa-0">
                                                            <MudTextField @bind-Value="TemplateSections[sectionIndex].Fields[fieldIndex].Instructions"
                                                                          Variant="Variant.Outlined"
                                                                          Margin="Margin.Dense"
                                                                          Class="mt-2 ml-5" />
                                                        </MudItem>
                                                    </div>
                                                </MudItem>
                                            </MudCardContent>
                                        </MudCard>
                                    }
                                }
                            }
                            <MudButton Size="Size.Small" Color="Color.Primary" OnClick="@SubmitForm" Typo="Typo.body2">
                                <MudIcon Icon="@Icons.Material.Filled.Send" Class="m-2" />
                                @Localizer["Submit Form"]
                            </MudButton>
                        </MudCardContent>
                    </MudCard>
                </MudItem>

            </MudItem>
        }
    </MudGrid>
</MudPaper>




<style>
    .medical-sections .e-list-item {
        padding: 12px 0;
        color: #5f6368;
        font-size: 14px;
        border-bottom: 1px solid #ebedf0;
    }

    .custom-disabled-list-item.mud-list-item-disabled {
        color: silver !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }

    .custom-disabled-listheader-item.mud-list-item-disabled {
        color: grey !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }
</style>