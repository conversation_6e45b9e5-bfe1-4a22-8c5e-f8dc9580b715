﻿@page "/Allergies"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using TeyaWebApp.Components.Layout
@using MudBlazor
@using TeyaAIScribeResource
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using TeyaUIViewModels

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" OnClick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_allergicdialog" Style="width: 85vw; max-width: 1200px;">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Allergies"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="3">
                        <MudSelect T="string"
                                   Label="@Localizer["Select Database"]"
                                   Value="selectedDatabase"
                                   ValueChanged="OnDatabaseChanged"
                                   Dense="true"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Style="width: 100%;">
                            <MudSelectItem T="string" Value="@("RxNorm")">@Localizer["RxNorm"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@("FDB")">@Localizer["FDB"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                </MudGrid>

                <!-- Drug name search and Add button row -->
                <MudGrid Spacing="3" Style="align-items: center; margin-top: 8px;">
                    <MudItem xs="4">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["SearchDN"]"
                                         Value="@drugName"
                                         ValueChanged="OnDrugNameChanged"
                                         SearchFunc="SearchDrugNames"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         Style="width: 100%;"
                                         MinCharacters="2" />
                    </MudItem>
                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewAllergy"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 100px; height: 40px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <SfGrid @ref="AllergyGrid" DataSource="@_localAllergiesData" TValue="Allergy" AllowPaging="true" GridLines="GridLine.Both" AllowSorting="true" Style="font-size: 0.85rem; margin-top: 24px;">
                    <GridPageSettings PageSize="5"></GridPageSettings>
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="Allergy"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="MedicineId" Visible="false" IsPrimaryKey="true"></GridColumn>
                        <GridColumn Field="DrugName" HeaderText="@Localizer["AllergyCol5"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                        <GridColumn Field="Classification" HeaderText="@Localizer["AllergyCol1"]" TextAlign="TextAlign.Center" Width="100">
                            <ValidationRules>
                                <RequiredRule Message="@Localizer["ClassificationRequired"]"></RequiredRule>
                                <RegexRule RegexPattern="^[A-Za-z]+$" Message="@Localizer["ClassificationInvalid"]"></RegexRule>
                            </ValidationRules>
                        </GridColumn>
                        <GridColumn Field="Agent" HeaderText="@Localizer["AllergyCol2"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="7777">
                            <ValidationRules>
                                <RequiredRule Message="@Localizer["AgentRequired"]"></RequiredRule>
                                <RegexRule RegexPattern="^[A-Za-z]+$" Message="@Localizer["AgentInvalid"]"></RegexRule>
                            </ValidationRules>
                        </GridColumn>
                        <GridColumn Field="Reaction" HeaderText="@Localizer["AllergyCol3"]" TextAlign="TextAlign.Center" Width="100">
                            <ValidationRules>
                                <RequiredRule Message="@Localizer["ReactionRequired"]"></RequiredRule>
                                <RegexRule RegexPattern="^[A-Za-z]+$" Message="@Localizer["ReactionInvalid"]"></RegexRule>
                             </ValidationRules>
                        </GridColumn>
                        <GridColumn Field="Type" HeaderText="@Localizer["AllergyCol4"]" TextAlign="TextAlign.Center" Width="100">
                            <ValidationRules>
                                <RequiredRule Message="@Localizer["TypeRequired"]"></RequiredRule>
                                <RegexRule RegexPattern="^[A-Za-z0-9\-]+$" Message="@Localizer["TypeInvalid"]"></RegexRule>
                            </ValidationRules>
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["AllergyCol6"]" TextAlign="TextAlign.Center" Width="60">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="@CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="@SaveChanges"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>