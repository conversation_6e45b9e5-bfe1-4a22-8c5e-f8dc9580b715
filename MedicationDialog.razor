﻿@using MudBlazor
@using Syncfusion.Blazor.Grids
@using TeyaWebApp.TeyaAIScribeResources
@inject ISnackbar Snackbar
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer

<MudDialog @ref="_dialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Current Medication"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <!-- Form Inputs Section -->
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="4">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search Brand Names"]"
                                         Value="@DrugName"
                                         ValueChanged="OnDrugNameChanged"
                                         SearchFunc="SearchBrandNames"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Margin="Margin.Dense"
                                         Variant="Variant.Outlined"
                                         MinCharacters="3"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="4">
                        <MudSelect T="string"
                                   Label="@Localizer["Dosage & InTake"]"
                                   @bind-Value="FinalSBD"
                                   Dense="true"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Style="width: 100%;">
                            @foreach (var drugs in BrandSBD)
                            {
                                <MudSelectItem T="string" Value="@drugs">@drugs</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewMedication"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 100px; height: 40px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- Medication Table -->
                <SfGrid @ref="MedicinesGrid"
                        TValue="ActiveMedication"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@Medications"
                        AllowPaging="true"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="ActiveMedication"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="BrandName" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                        <GridColumn Field="DrugDetails" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200"></GridColumn>
                        <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1"></GridColumn>
                        <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                        <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)"
                                    HeaderText="@Localizer["Chief Complaint"]"
                                    TextAlign="TextAlign.Center"
                                    Width="200"
                                    EditTemplate="@ChiefComplaintEditTemplate">
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>

@code {
    private MudDialog _dialog;
    [Inject] private IRxNormService RxNormService { get; set; }
    // Parameters that parent components can set
    [Parameter] public List<string> BrandNames { get; set; } = new();
    [Parameter] public List<ActiveMedication> Medications { get; set; } = new();
    [Parameter] public List<ChiefComplaintDTO> LocalData { get; set; } = new();
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }
    [Parameter] public Guid PatientID { get; set; }
    [Parameter] public ActiveUser User { get; set; }
    [Parameter] public PatientService PatientService { get; set; }

    // Internal state
    public List<string> BrandSBD { get; set; } = new();
    public string DrugName { get; set; }
    public string FinalSBD { get; set; }
    private string _Quantity;
    private string _Frequency;
    public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
    private List<ActiveMedication> deleteList { get; set; } = new();
    private List<ActiveMedication> AddList = new();
    private List<string> chiefComplaintDescriptions => LocalData?.Select(c => c.Description).ToList() ?? new();

    public RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
    {
        if (context is not ActiveMedication medication) return;

        builder.OpenComponent<SfDropDownList<string, string>>(0);
        builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
        builder.AddAttribute(2, "Value", medication.CheifComplaint);
        builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<string>(this, value => medication.CheifComplaint = value));
        builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
        builder.CloseComponent();
    };

    public void Open() => _dialog.ShowAsync();
    public void Close() => _dialog.CloseAsync();

    private async Task OnDrugNameChanged(string value)
    {
        DrugName = value;
        if (!string.IsNullOrEmpty(value))
        {
            BrandSBD = await RxNormService.GetSBDNamesAsync(value);
            FinalSBD = null;
            StateHasChanged();
        }
        else
        {
            BrandSBD.Clear();
            FinalSBD = null;
            StateHasChanged();
        }
    }

    protected Task<IEnumerable<string>> SearchBrandNames(string value, CancellationToken cancellationToken)
    {
        IEnumerable<string> result;
        if (cancellationToken.IsCancellationRequested)
        {
            result = Enumerable.Empty<string>();
        }
        else if (string.IsNullOrWhiteSpace(value))
        {
            result = BrandNames.AsEnumerable();
        }
        else
        {
            result = BrandNames.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
        }
        return Task.FromResult(result);
    }

    private async void AddNewMedication()
    {
        if (string.IsNullOrEmpty(DrugName) || string.IsNullOrEmpty(FinalSBD))
        {
            Snackbar.Add(@Localizer["Please fill in both Brand Name and Drug Details fields"], Severity.Warning);
            return;
        }
        var newMedication = new ActiveMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = PatientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = PatientService.PatientData.OrganizationID ?? Guid.Empty,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                BrandName = DrugName,
                DrugDetails = FinalSBD,
                Quantity = _Quantity ?? "Not Specified",
                Frequency = _Frequency ?? "Not Specified",
                isActive = true,
                StartDate = null,
                EndDate = null,
            };

        AddList.Add(newMedication);
        Medications.Add(newMedication);
        await MedicinesGrid.Refresh();
        ResetInputFields();
    }

    private async void ResetInputFields()
    {
        DrugName = string.Empty;
        FinalSBD = null;
        BrandSBD.Clear();
        _Quantity = null;
        _Frequency = null;
        await InvokeAsync(StateHasChanged);
    }

    public void ActionCompletedHandler(Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication> args)
    {
        if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
        {
            var deletedMedication = args.Data as ActiveMedication;
            var existingItem = AddList.FirstOrDefault(v => v.MedicineId == deletedMedication.MedicineId);

            if (existingItem != null)
            {
                AddList.Remove(existingItem);
            }
            else
            {
                args.Data.isActive = false;
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
                deleteList.Add(args.Data);
            }
        }
    }

    public void ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication> args)
    {
        if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
        {
            if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue &&
                args.Data.StartDate > args.Data.EndDate)
            {
                Snackbar.Add(@Localizer["Start Date cannot be after End Date"], Severity.Warning);
                args.Cancel = true;
                return;
            }
            args.Data.UpdatedBy = Guid.Parse(User.id);
            args.Data.UpdatedDate = DateTime.Now;
        }
    }

    private async Task HandleBackdropClick()
    {
        Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
    }

    private async Task SaveChanges()
    {
        await OnSave.InvokeAsync();
        Close();
    }

    private async Task CancelChanges()
    {
        await OnCancel.InvokeAsync();
        Close();
    }
}