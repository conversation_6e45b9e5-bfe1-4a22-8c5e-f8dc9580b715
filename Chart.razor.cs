﻿using Microsoft.AspNetCore.Components;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Represents a Blazor component for displaying patient chart data.
    /// </summary>
    public partial class Chart : ComponentBase
    {
        /// <summary>
        /// Injected service for managing patient data.
        /// </summary>
        [Inject]
        private PatientService PatientService { get; set; }
        private Guid OrdersetId;
        public List<CompleteOrderSet> resolvedOrderset { get; set; }
        /// <summary>
        /// Gets the patient data from the service or initializes a new instance if null.
        /// </summary>
        public Patient PatientData => PatientService.PatientData ?? new Patient();
        private int activeTabIndex;
        private Guid selectedNoteId;
        private bool showSingleRecordView;

        private Task OnTabChanged()
        {
            if ((PatientData?.Name == null && activeTabIndex == 1) || 
                 (PatientData?.Name != null && activeTabIndex == 2))   
            {
                return HandleGoBackToEncounters();
            }

            return Task.CompletedTask;
        }

        private void SwitchToNotesTab(Guid id)
        {
            selectedNoteId = id;
          
            activeTabIndex = (PatientData?.Name == null) ? 0 : 1;
        }
        private Task ClearSelectedNoteId()
        {
            selectedNoteId = Guid.Empty;
            showSingleRecordView = false;
            return Task.CompletedTask;
        }

        private Task HandleGoBackToEncounters()
        {
            selectedNoteId = Guid.Empty;
            showSingleRecordView = false;
            
            activeTabIndex = (PatientData?.Name == null) ? 1 : 2;
            return Task.CompletedTask;
        }
        private string CalculateAge(DateTime dateOfBirth)
        {
            DateTime today = DateTime.Today;
            int age = today.Year - dateOfBirth.Year;

            // Adjust age if birthday hasn't occurred yet this year
            if (dateOfBirth.Date > today.AddYears(-age))
            {
                age--;
            }

            return $"{age} {Localizer["years"]}";
        }
        private void SwitchTab()
        {
            activeTabIndex = 1;
        }
        private string GetTabClass(int index)
        {
            return activeTabIndex == index ? "tab-active" : "tab-inactive";
        }

       
       


    }
}
