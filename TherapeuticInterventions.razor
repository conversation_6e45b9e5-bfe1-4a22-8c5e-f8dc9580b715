﻿@page "/TherapeuticInterventions"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System
@using Syncfusion.Blazor.DropDowns
@using TeyaUIModels.Model;

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="Symbol">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                   OnClick="OpenNewDialogBox"
                                   Size="Size.Small" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>
<MudDialog @ref="__TherapeuticInterventions" Style="width: 80vw; max-width: 1100px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["TherapeuticInterventions"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="4">

                        <MudAutocomplete T="TherapeuticInterventionsListCode"
                                         Label="@Localizer["SearchTherapy"]"
                                         Value="TherapeuticInterventionsListName"
                                         ValueChanged="OnTherapeuticInterventionsListNameChanged"
                                         SearchFunc="SearchTherapeuticInterventionsListCodes"
                                         ToStringFunc="@(s=> s == null ? string.Empty : s.TherapyType)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         MinCharacters="2"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewTherapyType"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 70px; height: 35px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
                <SfGrid @ref="TherapeuticInterventionsGrid" TValue="TherapeuticInterventionsData" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@_TherapeuticInterventions" GridLines="GridLine.Both" AllowPaging="true" PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="TherapeuticInterventionsData"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="TherapeuticInterventionsID" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" AllowEditing="false" Format="yyyy-MM-dd" />
                        <GridColumn Field="TherapyType" HeaderText="@Localizer["TherapyType"]" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="UpdatedDate" HeaderText="@Localizer["UpdatedDate"]" TextAlign="TextAlign.Center" AllowEditing="false" Format="yyyy-MM-dd" />
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>