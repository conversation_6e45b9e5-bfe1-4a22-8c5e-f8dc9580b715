﻿.recording-dialog {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

.recording-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 23px;
    padding: 0px;
    min-height: 300px;
    margin-bottom: 20px;
}

.recording-indicator {
    background-color: #ff4757;
    color: white;
    padding: 6px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.recording-indicator-start {
    margin-left: 10px;
    background-color: #ff4757;
    color: white;
    padding: 6px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.wave-container {
    position: relative;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wave-group {
    display: flex;
    align-items: center;
    gap: 6px;
    height: 100%;
    padding: 0px;
}

.wave-bar {
    width: 4px;
    background-color: #198b87;
    border-radius: 15px;
    transition: height 0.1s ease;
    animation: none;
    height: var(--height);
    opacity: var(--opacity);
}

.controls-wrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 50px;
}

.timer {
    font-size: 28px;
    font-weight: 500;
    color: #000;
    min-width: 80px;
    text-align: center;
}

.timer-recording {
    font-size: 28px;
    font-weight: 500;
    color: #000;
    min-width: 80px;
    text-align: center;
}

::deep .start-visit-btn {
    background-color: #198b87 !important;
    color: white !important;
    padding: 5px 23px !important;
    border-radius: 50px !important;
    font-size: 18px !important;
    text-transform: none !important;
}

::deep .end-visit-btn {
    background-color: #198b87 !important;
    color: white !important;
    padding: 6px 20px !important;
    border-radius: 24px !important;
    text-transform: none !important;
    height: 40px !important;
    margin-bottom: 10px !important;
}

.pause-btn {
    margin-right: 60px;
    background-color: #198b87 !important;
    color: white !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
}

.processing-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 18px;
}

.processing-text {
    color: #666;
    font-size: 24px;
}

.spin {
    animation: spin 1s linear infinite;
    font-size: 88px;
    color: #198b87;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.dialog-header {
    display: flex;
    justify-content: flex-end;
}
