﻿@page "/signin"
@using BusinessLayer.Services
@using Microsoft.AspNetCore.Authorization
@using System.Text.Json;
@attribute [Authorize]
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.ViewModel
@inject GraphApiService AuthService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@rendermode InteractiveServer
@using TeyaUIViewModels
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Services
<MudProgressCircular Color="Color.Primary" Indeterminate="true" Style="@(isLoading ? "display: block; margin: 20px auto;" : "display: none;")" />

