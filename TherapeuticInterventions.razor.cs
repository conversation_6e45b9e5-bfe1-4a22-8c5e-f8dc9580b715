﻿using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class TherapeuticInterventions
    {
        [Inject] public ITherapeuticInterventionsListService _TherapeuticInterventionsListService { get; set; }
        [Inject] public ITherapeuticInterventionsService _TherapeuticInterventionsService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
       
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private ILogger<TherapeuticInterventions> _logger { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public TherapeuticInterventionsListCode TherapeuticInterventionsListName { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __TherapeuticInterventions;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrgID { get; set; }
        //private List<TherapeuticInterventionsListCode> _TherapeuticInterventionsListCodes { get; set; } = new List<TherapeuticInterventionsListCode>();
        public SfGrid<TeyaUIModels.Model.TherapeuticInterventionsData> TherapeuticInterventionsGrid { get; set; }

        private List<TeyaUIModels.Model.TherapeuticInterventionsData> AddList = new();
        private List<TeyaUIModels.Model.TherapeuticInterventionsData> _TherapeuticInterventions { get; set; }
        private List<TeyaUIModels.Model.TherapeuticInterventionsData> deleteTherapeuticInterventionslist { get; set; } = new List<TeyaUIModels.Model.TherapeuticInterventionsData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };

        private CancellationTokenSource _searchCancellationTokenSource;
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent { get; set; }

        /// <summary>
        /// Get All TherapeuticInterventionsList Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;
            activeUserOrganizationId = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            _TherapeuticInterventions = await _TherapeuticInterventionsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _TherapeuticInterventions
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p> <strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToString("MM-dd-yyyy") : Localizer["No date"])} : </strong> {Localizer["Therapy Type"]}: {s.TherapyType} , {Localizer["Notes"]}:  {s.Notes}</p>"));
        }

        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;

            string dynamicContent = (_TherapeuticInterventions != null && _TherapeuticInterventions.Any())
                ? string.Join("<br>", _TherapeuticInterventions
                    .OrderByDescending(s => s.CreatedDate)
                    .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])} <br><strong>{Localizer["Therapy Type"]}:</strong> {s.TherapyType} <br><strong>{Localizer["Notes"]}:</strong> {s.Notes}</p>"))
                : string.Empty;

            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {dynamicContent}
    </div>";
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
        private async Task OpenNewDialogBox()
        {
            await __TherapeuticInterventions.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __TherapeuticInterventions.CloseAsync();
        }


        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewTherapyType()
        {
            var newTherapyType = new TeyaUIModels.Model.TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrgID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                TherapyType = TherapeuticInterventionsListName.TherapyType,
                IsActive = true,
            };

            AddList.Add(newTherapyType);
            _TherapeuticInterventions.Add(newTherapyType);
            await TherapeuticInterventionsGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            TherapeuticInterventionsListName = new();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.TherapeuticInterventionsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleleTherapeuticInterventions = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.TherapeuticInterventionsID == deleleTherapeuticInterventions.TherapeuticInterventionsID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    deleleTherapeuticInterventions.IsActive = false;
                    deleleTherapeuticInterventions.UpdatedDate = DateTime.Now;
                    deleteTherapeuticInterventionslist.Add(deleleTherapeuticInterventions);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.TherapeuticInterventionsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _TherapeuticInterventionsService.AddTherapeuticInterventionsAsync(AddList, OrgID, Subscription);
            }
            await _TherapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(_TherapeuticInterventions, OrgID, Subscription);
            await _TherapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(deleteTherapeuticInterventionslist, OrgID, Subscription);
            deleteTherapeuticInterventionslist.Clear();
            AddList.Clear();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deleteTherapeuticInterventionslist.Clear();
            AddList.Clear();
            _TherapeuticInterventions = await _TherapeuticInterventionsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task<IEnumerable<TherapeuticInterventionsListCode>> SearchTherapeuticInterventionsListCodes(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<TherapeuticInterventionsListCode>();

            // Cancel previous search if still running
            _searchCancellationTokenSource?.Cancel();
            _searchCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Add debounce delay (300ms)
                await Task.Delay(300, _searchCancellationTokenSource.Token);

                // Call service with the current search term
                return await _TherapeuticInterventionsListService.GetTherapeuticInterventionsBySearchTerm(searchTerm);
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<TherapeuticInterventionsListCode>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to search medications");
                return Enumerable.Empty<TherapeuticInterventionsListCode>();
            }
        }
        /// <summary>
        /// Update Value in TherapeuticInterventionsList Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnTherapeuticInterventionsListNameChanged(TherapeuticInterventionsListCode value)
        {
            TherapeuticInterventionsListName = value;
            StateHasChanged();
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}