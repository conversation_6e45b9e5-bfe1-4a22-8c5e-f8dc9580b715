﻿using System.Text.RegularExpressions;
using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Templates;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
public static class ImageTypeExtensions
{
    public static string GetMimeType(this AllowedImageType imageType, IStringLocalizer localizer)
    {
        return imageType switch
        {
            AllowedImageType.Jpeg => localizer["image/jpeg"],
            AllowedImageType.Jpg => localizer["image/jpg"],
            AllowedImageType.Png => localizer["image/png"],
            AllowedImageType.Gif => localizer["image/gif"],
            AllowedImageType.Bmp => localizer["image/bmp"],
            AllowedImageType.Webp => localizer["image/webp"],
            AllowedImageType.Tiff => localizer["image/tiff"],
            _ => throw new ArgumentOutOfRangeException(nameof(imageType), localizer["Unsupported image type."])
        };
    }

    public static IEnumerable<string> GetAllMimeTypes(IStringLocalizer localizer)
    {
        return Enum.GetValues<AllowedImageType>().Select(type => type.GetMimeType(localizer));
    }
}
public enum AllowedImageType
{
    Jpeg,
    Jpg,
    Png,
    Gif,
    Bmp,
    Webp,
    Tiff
}

namespace TeyaWebApp.Components.Pages
{
    public partial class Patients
    {
        private Member member = new Member();
        private Address address = new Address();
        private Insurance insurance = new Insurance();
        private Guardian guardian = new Guardian();
        private Employer employer = new Employer();
        private bool showMemberForm = true;
        private bool showGuardianForm = false;
        private bool isAddingNewMember = false;
        private bool showEmployerForm = false;
        private bool showStatsForm = false;
        private bool showMiscellaneousForm = false;
        private bool showAddressForm = false;
        private bool photoUploadSuccess = false;
        private string photoUploadMessage = "";
        private string uploadedFileName = string.Empty;

        [Parameter]
        public Guid? memberId { get; set; }
        private const int DefaultPasswordLength = 12;

        [Inject]
        private ILogger<Patients> Logger { get; set; }
        [Inject] private ICommunicationService CommunicationService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        private IBrowserFile selectedFile;
        private string signInType, issuer;
        [Inject] private GraphApiService _GraphApiService { get; set; } = default!;
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private ICountryService countryService { get; set; }

        private bool Subscription = false;

        // Method using the now properly-defined extension methods

        private string? imagePreviewUrl;
        private bool showPreview = false;

        private MudDatePicker? dobDatePicker;
        private MudDatePicker? financialReviewDatePicker;
        private MudDatePicker? effectiveDatePicker;
        private MudDatePicker? deceasedDatePicker;

        private async Task HandleFileUpload(IBrowserFile file, IStringLocalizer localizer)
        {
            uploadedFileName = file.Name;
            try
            {
                if (file != null)
                {
                    // Get allowed image MIME types from enum
                    var allowedImageTypes = ImageTypeExtensions.GetAllMimeTypes(localizer);

                    if (!allowedImageTypes.Contains(file.ContentType.ToLower()))
                    {
                        photoUploadSuccess = false;
                        photoUploadMessage = $"Only image files are allowed ({string.Join(", ", Enum.GetNames<AllowedImageType>())}).";
                        Snackbar.Add(Localizer["Invalidfiletype"], Severity.Warning);
                        return;
                    }

                    if (file.Size > 10 * 1024 * 1024) // 10MB in bytes
                    {
                        photoUploadSuccess = false;
                        photoUploadMessage = Localizer["Imagefilesize"];
                        Snackbar.Add(Localizer["Filetoolarge"], Severity.Warning);
                        return;
                    }

                    // Generating preview
                    using (var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024))
                    using (var ms = new MemoryStream())
                    {
                        await stream.CopyToAsync(ms);
                        var base64 = Convert.ToBase64String(ms.ToArray());
                        imagePreviewUrl = $"data:{file.ContentType};base64,{base64}";
                    }
                    showPreview = false;

                    var azureBlobService = new AzureBlobService();
                    string imageUrl = await azureBlobService.UploadImageAsync(file);
                    member.ProfileImageUrl = imageUrl;
                    photoUploadSuccess = true;
                    photoUploadMessage = "Image added successfully!";
                    Snackbar.Add(Localizer["Image uploaded successfully"], Severity.Success);
                    await Task.Delay(5000);
                    photoUploadSuccess = false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error uploading Image");
                photoUploadSuccess = false;
                photoUploadMessage = $"Error uploading Image: {ex.Message}";
                Snackbar.Add(Localizer["FailedImage"], Severity.Error);
            }
        }

        private void TogglePreview()
        {
            showPreview = !showPreview;
        }


        private Task HandleFileUploadWrapper(IBrowserFile file)
        {
            return HandleFileUpload(file, Localizer);
        }

        private async Task RemovePatientPhoto()
        {
            try
            {
                member.ProfileImageUrl = string.Empty;
                uploadedFileName = string.Empty;
                Snackbar.Add(Localizer["PatientImagesuccessful"], Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer[$"FailedImages"], Severity.Error);
            }
        }
        private void ClearPhotoUploadMessage()
        {
            photoUploadSuccess = false;
            photoUploadMessage = "";
        }
        protected override void OnInitialized()
        {
            signInType = Localizer["emailAddress"];
            issuer = Environment.GetEnvironmentVariable("PrimaryDomain") ?? string.Empty;
        }
        protected override async Task OnParametersSetAsync()
        {
            if (memberId.HasValue)
            {
                var orgId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(orgId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                member = await MemberService.GetMemberByIdAsync(memberId.Value, orgId, Subscription);
                isAddingNewMember = false;
            }
            else
            {
                isAddingNewMember = true;
                member = new Member();
            }
        }
        private void ToggleMiscellaneousForm()
        {
            showMiscellaneousForm = !showMiscellaneousForm;
        }
        private void ToggleStatsForm()
        {
            showStatsForm = !showStatsForm;
        }

        private bool showInsuranceForm = false;

        private void ToggleInsuranceForm()
        {
            showInsuranceForm = !showInsuranceForm;
        }
        private void ToggleAddressForm()
        {
            showAddressForm = !showAddressForm;
        }

        private void ToggleMemberForm()
        {
            showMemberForm = !showMemberForm;
        }

        private void ToggleGuardianForm()
        {
            showGuardianForm = !showGuardianForm;
        }

        private void ToggleEmployerForm()
        {
            showEmployerForm = !showEmployerForm;
        }
        private async Task HandleSubmit()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(member.UserName) ||
                    string.IsNullOrWhiteSpace(member.Email) ||
                    string.IsNullOrWhiteSpace(member.FirstName) ||
                    string.IsNullOrWhiteSpace(member.LastName) ||
                    string.IsNullOrWhiteSpace(member.Country))
                {
                    Snackbar.Add(Localizer["Please Fill All Required Fields"], Severity.Warning);
                    return;
                }

                string dialogTitle = isAddingNewMember ?
                    Localizer["ConfirmPatientCreation"] :
                    Localizer["ConfirmUpdate"];

                string dialogMessage = isAddingNewMember ?
                    Localizer["Patient.Create"] :
                    Localizer["Patient.Update"];

                bool? result = await DialogService.ShowMessageBox(
                    dialogTitle,
                    dialogMessage,
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                if (result != true)
                {
                    return;
                }

                if (member.Id == Guid.Empty)
                {
                    try
                    {
                        string password = GenerateRandomPassword();
                        string role = "Patient";
                        string id = await _GraphApiService.CreateUserAsync2(member.UserName, member.Email, password, signInType, issuer, member.Email, role);

                        if (Guid.TryParse(id, out Guid parsedGuid))
                        {
                            member.Id = parsedGuid;
                        }
                        var recipientList = new List<string> { member.Email };
                        var response = await RenderComponentHtml(member.Email, password);
                        var sendMail = await CommunicationService.SendHtmlEmailService(
                            Localizer["EmailSenderAddress"], Localizer["EmailSubject"], response, recipientList);

                        if (sendMail.HasCompleted)
                        {
                            Logger.LogInformation(Localizer["EmailCreatedSuccessful"]);
                        }
                        else
                        {
                            Logger.LogWarning(Localizer["EmailPendingOrFailedMessage"]);
                        }
                    }
                    catch
                    {
                        throw new Exception(Localizer["FailedAddingUser"]);
                    }
                }

                if (isAddingNewMember)
                {
                    member.IsActive = true;        //should be fixed 
                    insurance.InsuranceId = Guid.NewGuid();
                    var insuranceAdded = await InsuranceService.AddInsuranceAsync(insurance);
                    if (!insuranceAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                        return;
                    }

                    address.AddressId = Guid.NewGuid();
                    var addressAdded = await AddressService.AddAddressAsync(address);
                    if (!addressAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                        return;
                    }

                    guardian.GuardianId = Guid.NewGuid();
                    var GuardianAdded = await GuardianService.AddGuardianAsync(guardian);
                    if (!GuardianAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                        return;
                    }

                    employer.EmployerId = Guid.NewGuid();
                    var employerAdded = await EmployerService.AddEmployerAsync(employer);
                    if (!employerAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                        return;
                    }
                    member.AddressId = address.AddressId;
                    member.InsuranceId = insurance.InsuranceId;
                    member.GuardianId = guardian.GuardianId;
                    member.EmployerId = employer.EmployerId;
                    member.RoleName = "Patient";
                    member.OrganizationName = User.OrganizationName;
                    member.OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                    var roles = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
                    roles = roles.Where(role => role.RoleName == "Patient").ToList();
                    member.RoleID = roles.FirstOrDefault()?.RoleId
                         ?? throw new Exception(Localizer["RoleNotFoundError"]);
                    var updateFields = new Dictionary<string, object>
                        {
                            { $"{Environment.GetEnvironmentVariable("EXTENSION-PREFIX")}_OrganizationName",member.OrganizationName},
                            { "Country",member.Country},
                            { "GivenName",member.FirstName },
                            { "SurName",member.LastName },
                        };
                    bool updateSuccessful = await _GraphApiService.UpdateUserProfileAsync(member.Id.ToString(), updateFields);
                    if (!updateSuccessful)
                    {
                        Logger.LogError(Localizer["FailedToUpdateDisplayName"]);
                    }

                    var registeredMember = await MemberService.RegisterMembersAsync(new List<Member> { member });
                    Logger.LogInformation(Localizer["MemberRegistered"], registeredMember.UserName);

                    Snackbar.Add(Localizer["Patient created successfully"], Severity.Success);
                    ResetForm();
                }
                else
                {
                    await MemberService.UpdateMemberByIdAsync(member.Id, member);
                    Logger.LogInformation(Localizer["MemberUpdated"], member.UserName);
                    Snackbar.Add(Localizer["Patient Details updated successfully"], Severity.Success);
                }
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingMemberData"]);
                Snackbar.Add(Localizer["Error processing request. Please try again."], Severity.Error);
            }
        }

        public async Task<string> RenderComponentHtml(string email, string password)
        {
            try
            {
                inviteMailParametersService.Email = email;
                inviteMailParametersService.Password = password;
                string renderedHtml = await RazorRenderer.RenderComponentToHtmlAsync<InviteMail>();
                return renderedHtml;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["RenderError"]);
                return string.Empty;
            }
        }
        private string GenerateRandomPassword(int length = DefaultPasswordLength)
        {
            string chars = Localizer["RandomPasswordChars"];

            Random random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                                         .Select(charSet => charSet[random.Next(charSet.Length)]).ToArray());
        }
        private void ResetForm()
        {
            member = new Member();
            address = new Address();
            insurance = new Insurance();
            guardian = new Guardian();
            employer = new Employer();
            isAddingNewMember = true;
            showMemberForm = false;
            showGuardianForm = false;
            showEmployerForm = false;
            showStatsForm = false;
            showMiscellaneousForm = false;
            showAddressForm = false;
            showInsuranceForm = false;
        }

        private async Task HandleDelete()
        {
            try
            {
                if (member.Id != Guid.Empty)
                {
                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["ConfirmDeletion"],
                        Localizer["Confirm.Deletion"],
                        yesText: Localizer["Delete"],
                        noText: Localizer["Cancel"]);

                    if (result != true)
                    {
                        return;
                    }

                    var activeUserOrganizationId = member.OrganizationID ?? Guid.NewGuid();
                    await MemberService.DeleteMemberByIdAsync(member.Id, activeUserOrganizationId, Subscription);
                    Logger.LogInformation(Localizer["MemberDeleted"], member.UserName);
                    Snackbar.Add(Localizer["Patientdelete"], Severity.Success);
                    Navigation.NavigateTo(Localizer["PatientsPagePath"]);
                }
                ResetForm();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDeletingMember"]);
                Snackbar.Add(Localizer["Errordelete"], Severity.Error);
            }
            StateHasChanged();
        }

        /// <summary>
        /// Validates the Phone Number 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidatePhoneNumber(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = new string(value.Where(char.IsDigit).ToArray());

                if (digitsOnly.Length != 10)
                {
                    result = "Invalid phone number. Must be 10 digits (e.g., ************).";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the SSN
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateSSN(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = new string(value.Where(char.IsDigit).ToArray());

                if (digitsOnly.Length != 9)
                {
                    result = "Invalid SSN. Must be 9 digits (e.g., ***********).";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Postal Code 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidatePostalCode(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = new string(value.Where(char.IsDigit).ToArray());

                if (digitsOnly.Length != 5 && digitsOnly.Length != 9)
                {
                    result = "Invalid postal code. Must be 5 or 9 digits (e.g., 12345 or 12345-6789).";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Email format
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateEmail(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                try
                {
                    var addr = new System.Net.Mail.MailAddress(value);
                }
                catch
                {
                    result = "Invalid email format.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Alphabetic input only alphabetic characters and spaces are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAlphabetic(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' '))
                {
                    result = "Invalid input. Only alphabetic characters and spaces are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Alphanumeric input only alphanumeric characters and spaces are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAlphanumeric(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetterOrDigit(c) || c == ' '))
                {
                    result = "Invalid input. Only alphanumeric characters and spaces are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Decimal input only numbers and a decimal point are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateDecimal(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!decimal.TryParse(value, out _))
                {
                    result = "Invalid decimal input. Only numbers and a decimal point are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Numeric input only numbers are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateNumeric(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(char.IsDigit))
                {
                    result = "Invalid input. Only numbers are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Address input only letters, numbers, spaces, commas, periods, and hyphens are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAddress(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();

                if (!Regex.IsMatch(value, @"^[a-zA-Z0-9\s,.\-+]+$"))
                {
                    result = "Invalid address format. Only letters, numbers, spaces, commas, periods, and hyphens are allowed.";
                }
            }

            return result;
        }


        /// <summary>
        /// Fetch the country dropdown 
        /// </summary>
        private List<Country> _allCountries = new();

        private async Task<IEnumerable<string>> SearchCountries(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return new List<string>();


            if (_allCountries.Count == 0)
            {
                try
                {
                    _allCountries = (await countryService.GetAllCountriesAsync()).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching countries: {ex.Message}");
                    return new List<string>();
                }
            }

            return _allCountries
                .Where(c => c.CountryName.StartsWith(value, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.CountryName)
                .ToList();
        }
        /// <summary>
        /// Cross icon in the Date Picker
        /// </summary>

        // Shared close method
        private async Task CloseDatePicker(MudDatePicker picker)
        {
            if (picker is not null)
            {
                await picker.ClearAsync();
                await picker.CloseAsync();
            }
        }


    }
}
