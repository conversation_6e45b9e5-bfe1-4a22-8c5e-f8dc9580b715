﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using static TeyaWebApp.Components.Pages.Notes;
using static MudBlazor.Icons.Custom;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Net.Http;
using Syncfusion.Blazor.DropDowns;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Microsoft.Azure.Amqp.Framing;

namespace TeyaWebApp.Components.Pages
{
    public partial class Hospitalization : ComponentBase
    {
        private MudDialog _addMemberDialog;
        private List<string> ToolbarItems = new List<string> { "Add" };
        public SfGrid<HospitalizationRecord> hospitalizationRecordGrid { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        public HospitalizationRecord newVal = new();
        private List<HospitalizationRecord> records { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
       
        [Inject] private IDialogService DialogService { get; set; }
       
        private Guid patientID { get; set; }
        private Guid? organizationId { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ILogger<Config> Logger { get; set; }
        private bool add = false;
        private List<HospitalizationRecord> DeleteList = new();
        private List<HospitalizationRecord> AddList = new();



        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }


        /// <summary>
        /// get list of Hospitalization Record from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {

                patientID = PatientID;
                organizationId = OrgId;
                ManualContent = Data;

                Subscription = UserContext.ActiveUserSubscription;
            records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(patientID, organizationId, Subscription);
                editorContent = GenerateRichTextContent(ManualContent);

        }


        private async Task ActionBeginHandler(ActionEventArgs<HospitalizationRecord> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                        Localizer["ConfirmDelete"],
                        Localizer["DeleteConfirmationMessage"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["No"]);


                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                
            }
        }
        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>
        private void ActionCompletedHandler(ActionEventArgs<HospitalizationRecord> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedRecord = args.Data as HospitalizationRecord;
                    var existingItem = AddList.FirstOrDefault(v => v.RecordID == deletedRecord.RecordID);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedRecord.IsActive = false;
                        deletedRecord.UpdatedBy = Guid.Parse(User.id);
                        deletedRecord.UpdatedDate = DateTime.Now;
                        args.Data.UpdatedBy = patientID;
                        DeleteList.Add(deletedRecord);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.PatientId = patientID;
                args.Data.OrganizationID = organizationId;
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = patientID;
                args.Data.RecordID = new Guid();
                args.Data.IsActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals);
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Add HospitalizationRecord to database
        /// </summary>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await HospitalizationRecordService.CreateHospitalizationRecordAsync(AddList, organizationId, Subscription);
            }
            await HospitalizationRecordService.UpdateHospitalizationRecordList(DeleteList, organizationId, Subscription);
            await HospitalizationRecordService.UpdateHospitalizationRecordList(records, organizationId, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(patientID, organizationId, Subscription);
            
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
          
            //ViewHandler();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _addMemberDialog.ShowAsync();
        }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add", TooltipText = "Add Record" },
        };

        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "";

            foreach (var member in records)
            {
                str += $"<li><b>{member.Date:dd-MM-yyyy}:</b> {member.Reason}</li>";
            }

            str += "</ul>";

            editorContent = str;
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _addMemberDialog.CloseAsync();
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>
        private async Task CancelChanges()
        {
            DeleteList.Clear();
            AddList.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(patientID, organizationId, Subscription);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        private async void CancelAction()
        {
            CloseAddTaskDialog();
            //ViewHandler();
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }

        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;
            string dynamicContent = records != null
                ? string.Join(" ", records
                    .Where(r => !string.IsNullOrEmpty(r.Reason))
                    .OrderByDescending(r => r.Date)
                    .Select(r => $"<ul><li style='margin-left: 20px;'><b>{r.Date:dd-MM-yyyy}:</b> {r.Reason}</li></ul>"))
                : string.Empty;
            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {dynamicContent}
    </div>";
        }




        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}